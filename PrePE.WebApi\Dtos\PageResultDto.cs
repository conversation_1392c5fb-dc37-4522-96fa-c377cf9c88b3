﻿namespace PrePE.WebApi.Dtos;

public class PageResultDto<T>(int pageIndex, int pageSize, int totalCount, List<T> items)
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; } = pageIndex;

    /// <summary>
    /// 也尺寸
    /// </summary>
    public int PageSize { get; set; } = pageSize;

    /// <summary>
    /// 数据总行数
    /// </summary>
    public int TotalCount { get; } = totalCount;

    /// <summary>
    /// 当前页数据列表
    /// </summary>
    public List<T> Items { get; set; } = items;
}
