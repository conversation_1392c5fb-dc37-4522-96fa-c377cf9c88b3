﻿using PrePE.Domain.AppointmentMng.Entities;

namespace PrePE.Domain.AppointmentMng.Repositories
{
    public interface INumberSourceTemplateRepository : IBaseRepository<NumberSourceTemplate>
    {
        /// <summary>
        /// 获取所有时间段模板
        /// </summary>
        /// <returns>时间段模板列表</returns>
        Task<List<NumberSourceTemplate>> GetAllTemplatesAsync();

        ///// <summary>
        ///// 获取时间段模板
        ///// </summary>
        ///// <param name="id">模板ID</param>
        ///// <returns>时间段模板</returns>
        //Task<NumberSourceTemplate?> GetTemplateByIdAsync(long id);

        ///// <summary>
        ///// 添加时间段模板
        ///// </summary>
        ///// <param name="template">时间段模板</param>
        ///// <returns>添加后的模板ID</returns>
        //Task<long> AddTemplateAsync(NumberSourceTemplate template);

        ///// <summary>
        ///// 更新时间段模板
        ///// </summary>
        ///// <param name="template">时间段模板</param>
        ///// <returns>更新结果</returns>
        //Task<bool> UpdateTemplateAsync(NumberSourceTemplate template);

        ///// <summary>
        ///// 删除时间段模板
        ///// </summary>
        ///// <param name="id">模板ID</param>
        ///// <returns>删除结果</returns>
        //Task<bool> DeleteTemplateAsync(long id);
    }
}
