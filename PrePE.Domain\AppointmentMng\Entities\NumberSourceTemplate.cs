﻿using PrePE.Domain.AppointmentMng.Enums;

namespace PrePE.Domain.AppointmentMng.Entities;

[SugarTable("appt_number_source_template", TableDescription = "号源模板")]
public class NumberSourceTemplate : Entity, IAggregateRoot
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 开始分钟数
    /// </summary>
    [SugarColumn(ColumnName = "begin_minute", ColumnDescription = "开始分钟数")]
    public int BeginMinute { get; set; }

    /// <summary>
    /// 结束分钟数
    /// </summary>
    [SugarColumn(ColumnName = "end_minute", ColumnDescription = "结束分钟数")]
    public int EndMinute { get; set; }

    /// <summary>
    /// 号源类型
    /// </summary>
    [SugarColumn(ColumnName = "source_type", ColumnDescription = "号源类型")]
    public NumberSourceType SourceType { get; set; }

    /// <summary>
    /// 特殊号源项目编码
    /// </summary>
    [SugarColumn(ColumnName = "special_item_code", ColumnDescription = "特殊号源项目编码")]
    public string? SpecialItemCode { get; set; }

    /// <summary>
    /// 特殊号源项目名称
    /// </summary>
    [SugarColumn(ColumnName = "special_item_name", ColumnDescription = "特殊号源项目名称")]
    public string? SpecialItemName { get; set; }

    public override object GetIdentity()
    {
        return Id;
    }
}
