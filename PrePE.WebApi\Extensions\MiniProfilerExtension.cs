﻿using StackExchange.Profiling.Storage;

namespace PrePE.WebApi.Extensions
{
    public static class MiniProfilerExtension
    {
        public static void AddMiniProfiler(this IServiceCollection services)
        {
            services.AddMiniProfiler(options =>
            {
                //访问地址路由根目录；默认为：/mini-profiler-resources
                options.RouteBasePath = "/profiler";
                //数据缓存时间
                (options.Storage as MemoryCacheStorage)!.CacheDuration = TimeSpan.FromMinutes(30);
                //sql格式化设置
                options.SqlFormatter = new StackExchange.Profiling.SqlFormatters.InlineFormatter();
                //跟踪连接打开关闭
                options.TrackConnectionOpenClose = true;
                //界面主题颜色方案;默认浅色
                options.ColorScheme = StackExchange.Profiling.ColorScheme.Dark;
                //.net core 3.0以上：对MVC过滤器进行分析
                options.EnableMvcFilterProfiling = true;
                //对视图进行分析
                //options.EnableMvcViewProfiling = true;
                //控制访问页面授权，默认所有人都能访问
                //options.ResultsAuthorize;
                //要控制分析哪些请求，默认说有请求都分析
                //options.ShouldProfile;
                //内部异常处理
                //options.OnInternalError = e => MyExceptionLogger(e);
            });
        }
    }
}
