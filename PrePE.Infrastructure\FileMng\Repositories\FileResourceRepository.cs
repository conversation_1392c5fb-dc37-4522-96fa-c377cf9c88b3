using PrePE.Domain.FileMng.Entities;
using PrePE.Domain.FileMng.Ropositories;
using PrePE.Common.Utils;
using Microsoft.AspNetCore.Http;

namespace PrePE.Infrastructure.FileMng.Repositories;

public class FileResourceRepository(HealthDbContext dbContext)
    : BaseRepository<FileResource>(dbContext), IFileResourceRepository
{

    //public override Task IncludeAsync(FileResource entity)
    //{
    //    throw new NotImplementedException();
    //}

    public Task<Guid> GetSliceFileIdAsync(string fileName)
    {
        throw new NotImplementedException();
    }

    public Task UploadSliceFileAsync(IFormFile sliceFile)
    {
        throw new NotImplementedException();
    }

    public async Task<FileResource> UploadFullFilesAsync(IFormFile files)
    {
        var fileName = Path.GetFileName(files.FileName);
        var result = FileResource.Create(fileName);
        var insertResult = await DbContext.FileResources.InsertAsync(result);
        if (insertResult)
        {
            var absolutePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, result.FilePath);
            await FileUtil.CreatFileAsync(absolutePath, files.OpenReadStream());
            return result;
        }
        else
        {
            throw new FileNotFoundException();
        }
    }

}