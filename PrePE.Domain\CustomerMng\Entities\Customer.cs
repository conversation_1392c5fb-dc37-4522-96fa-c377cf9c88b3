﻿namespace PrePE.Domain.CustomerMng.Entities;

/// <summary>
/// 客户
/// </summary>
[SugarTable("sys_customers", TableDescription = "客户")]
[SugarIndex("unique_{table}_phone_no", nameof(PhoneNo), OrderByType.Asc, true)]
public class Customer : Entity, IAggregateRoot
{
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "姓名")]
    public string Name { get; set; }

    /// <summary>
    /// 微信OpenId
    /// </summary>
    [SugarColumn(ColumnName = "wechat_openid", Length = 100, ColumnDescription = "微信OpentId")]
    public string? WechatOpenId { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    [SugarColumn(ColumnName = "phone_no", ColumnDescription = "手机号")]
    public string PhoneNo { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    [SugarColumn(ColumnName = "card_no", ColumnDescription = "证件号")]
    public string? CardNo { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    [SugarColumn(ColumnName = "card_type", ColumnDescription = "证件类型")]
    public int? CardType { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    [SugarColumn(ColumnName = "birthday", ColumnDescription = "生日")]
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [SugarColumn(ColumnName = "sex", ColumnDescription = "性别")]
    public int Sex { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    [SugarColumn(ColumnName = "job", ColumnDescription = "职业")]
    public string? Job { get; set; }

    /// <summary>
    /// 民族
    /// </summary>
    [SugarColumn(ColumnName = "nationality", ColumnDescription = "民族")]
    public string? Nationality { get; set; }

    /// <summary>
    /// 婚姻状态
    /// </summary>
    [SugarColumn(ColumnName = "marriage", ColumnDescription = "婚姻状态")]
    public string? Marriage { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}
