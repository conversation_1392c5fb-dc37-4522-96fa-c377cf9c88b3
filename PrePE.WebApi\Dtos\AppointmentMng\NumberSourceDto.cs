﻿using PrePE.Domain.AppointmentMng.Enums;
using SqlSugar;

namespace PrePE.WebApi.Dtos.AppointmentMng
{
    /// <summary>
    /// 号源DTO
    /// </summary>
    public class NumberSourceDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 排班日期
        /// </summary>
        public DateTime ScheduleDate { get; set; }

        /// <summary>
        /// 开始分钟数
        /// </summary>
        public int BeginMinute { get; set; }

        /// <summary>
        /// 结束分钟数
        /// </summary>
        public int EndMinute { get; set; }

        /// <summary>
        /// 号源类型
        /// </summary>
        public NumberSourceType SourceType { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 已预约数量
        /// </summary>
        public int BookedCount { get; set; }

        /// <summary>
        /// 剩余数量
        /// </summary>
        public int RemainingCount => TotalCount - BookedCount;

        /// <summary>
        /// 是否休息日
        /// </summary>
        public bool IsRestDay { get; set; }
    }

    /// <summary>
    /// 创建号源请求
    /// </summary>
    public class CreateNumberSourceRequest
    {
        /// <summary>
        /// 排班日期
        /// </summary>
        public DateTime ScheduleDate { get; set; }

        /// <summary>
        /// 时间段ID
        /// </summary>
        public int TimeSlotId { get; set; }

        /// <summary>
        /// 号源类型
        /// </summary>
        public NumberSourceType SourceType { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }
    }

    /// <summary>
    /// 批量创建号源请求
    /// </summary>
    public class BatchCreateNumberSourceRequest
    {
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime BeginDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 号源类型
        /// </summary>
        public NumberSourceType SourceType { get; set; }

        /// <summary>
        /// 特殊项目编码
        /// </summary>
        public string? SpecialItemCode { get; set; }

        /// <summary>
        /// 特殊项目名称
        /// </summary>
        public string? SpecialItemName { get; set; }

        /// <summary>
        /// 号源列表
        /// </summary>
        public List<NumberSourceItem> Items { get; set; } = [];

        ///// <summary>
        ///// 创建人ID
        ///// </summary>
        //public long CreatorId { get; set; }
    }

    /// <summary>
    /// 号源项
    /// </summary>
    public class NumberSourceItem
    {
        /// <summary>
        /// 开始分钟
        /// </summary>
        public int BeginMinute { get; set; }

        /// <summary>
        /// 结束分钟
        /// </summary>
        public int EndMinute { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }
    }

    /// <summary>
    /// 更新号源请求
    /// </summary>
    public class UpdateNumberSourceRequest
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 号源类型
        /// </summary>
        public NumberSourceType SourceType { get; set; }

        /// <summary>
        /// 特殊项目编码
        /// </summary>
        public string? SpecialItemCode { get; set; }

        /// <summary>
        /// 特殊项目名称
        /// </summary>
        public string? SpecialItemName { get; set; }

        /// <summary>
        /// 号源列表
        /// </summary>
        public List<NumberSourceItem> Items { get; set; } = [];
    }

    /// <summary>
    /// 查询号源请求
    /// </summary>
    public class QueryNumberSourceRequest
    {
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        ///// <summary>
        ///// 号源类型
        ///// </summary>
        //public NumberSourceType? SourceType { get; set; }
    }

    /// <summary>
    /// 按日期范围批量创建号源请求
    /// </summary>
    public class CreateNumberSourcesRequest
    {
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 号源类型
        /// </summary>
        public NumberSourceType SourceType { get; set; }

        /// <summary>
        /// 特殊项目编码（仅当号源类型为特殊项目时使用）
        /// </summary>
        public string? SpecialItemCode { get; set; }

        /// <summary>
        /// 特殊项目名称（仅当号源类型为特殊项目时使用）
        /// </summary>
        public string? SpecialItemName { get; set; }

        /// <summary>
        /// 号源项列表
        /// </summary>
        public List<NumberSourceItem> Items { get; set; } = [];
    }

    /// <summary>
    /// 休息日DTO
    /// </summary>
    public class RestDayDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 休息日类型
        /// </summary>
        public RestDayType Type { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 创建休息日请求
    /// </summary>
    public class CreateRestDayRequest
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 休息日类型
        /// </summary>
        public RestDayType Type { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }
    }
}
