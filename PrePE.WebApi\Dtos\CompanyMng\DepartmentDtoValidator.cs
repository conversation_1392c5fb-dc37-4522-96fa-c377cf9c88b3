using FluentValidation;

namespace PrePE.WebApi.Dtos.CompanyMng;

/// <summary>
/// 创建部门请求验证器
/// </summary>
public class CreateDepartmentRequestValidator : AbstractValidator<CreateDepartmentRequest>
{
    public CreateDepartmentRequestValidator()
    {
        RuleFor(x => x.CompanyId)
            .GreaterThan(0).WithMessage("公司ID必须大于0");

        RuleFor(x => x.DepartmentName)
            .NotEmpty().WithMessage("部门名称不能为空")
            .MaximumLength(200).WithMessage("部门名称长度不能超过200个字符");
    }
}

/// <summary>
/// 更新部门请求验证器
/// </summary>
public class UpdateDepartmentRequestValidator : AbstractValidator<UpdateDepartmentRequest>
{
    public UpdateDepartmentRequestValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0).WithMessage("部门ID必须大于0");

        RuleFor(x => x.DepartmentName)
            .MaximumLength(200).WithMessage("部门名称长度不能超过200个字符")
            .When(x => !string.IsNullOrEmpty(x.DepartmentName));
    }
}
