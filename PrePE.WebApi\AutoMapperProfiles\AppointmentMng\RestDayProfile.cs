﻿﻿﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.WebApi.Dtos.AppointmentMng;

namespace PrePE.WebApi.AutoMapperProfiles.AppointmentMng
{
    public class RestDayProfile : Profile
    {
        public RestDayProfile()
        {
            CreateMap<RestDay, RestDayDto>().ReverseMap();
            CreateMap<CreateRestDayRequest, RestDay>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.Now));
        }
    }
}
