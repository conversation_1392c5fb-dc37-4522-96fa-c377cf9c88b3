﻿namespace PrePE.WebApi.Tests.Fakers
{
    //internal class AssignDoctorDtoFaker : BaseFaker<AssignDoctorDto>
    //{
    //    public AssignDoctorDtoFaker()
    //    {
    //        DataFaker = new Faker<AssignDoctorDto>()
    //            .RuleFor(x => x.DoctorId, 1)
    //            .RuleFor(x => x.GradeCode, 1)
    //            .RuleFor(x => x.LastingDays, faker => faker.Random.Number(1, 365))
    //            .RuleFor(x => x.ManageWays,
    //                faker => Enumerable.Range(1, 3).Select(i => faker.Lorem.Word()).ToArray()
    //            )
    //        ;
    //    }
    //}
}
