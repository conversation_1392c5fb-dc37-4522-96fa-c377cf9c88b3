using PrePE.Domain.ExaminationMng.Entities;

namespace PrePE.Domain.ExaminationMng.Relations;

/// <summary>
/// 客户的体检单关系接口
/// </summary>
public interface IExaminationFormOfCustomer
{
    /// <summary>
    /// 添加体检单
    /// </summary>
    /// <param name="examinationForm">体检单</param>
    /// <returns></returns>
    Task AddAsync(ExaminationForm examinationForm);

    /// <summary>
    /// 获取客户的体检单列表
    /// </summary>
    /// <returns>体检单列表</returns>
    Task<List<ExaminationForm>> GetAllAsync();

    /// <summary>
    /// 根据预约ID获取体检单
    /// </summary>
    /// <param name="appointmentId">预约ID</param>
    /// <returns>体检单</returns>
    Task<ExaminationForm?> GetByAppointmentIdAsync(string appointmentId);
}
