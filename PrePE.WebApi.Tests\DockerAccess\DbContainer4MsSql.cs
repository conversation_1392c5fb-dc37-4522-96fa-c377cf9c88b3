﻿using DotNet.Testcontainers.Builders;
using SqlSugar;
using Testcontainers.MsSql;

namespace PrePE.WebApi.Tests.DockerAccess;

internal class DbContainer4MsSql : IDockerContainer
{
    readonly MsSqlContainer _dbContainer = new MsSqlBuilder()
            .WithImage("mcr.microsoft.com/mssql/server:2022-latest")
            .WithPassword("Asdf1234.")
            .WithEnvironment("ACCEPT_EULA", "Y")
            .WithPortBinding(2433, 1433)
            .WithWaitStrategy(Wait.ForUnixContainer().UntilPortIsAvailable(1433))
            .Build();


    public string GetConnectionString()
    {
        return _dbContainer.GetConnectionString();
    }

    public DbType GetDbType()
    {
        return DbType.SqlServer;
    }

    public Task StartAsync()
    {
        return _dbContainer.StartAsync();
    }

    public ValueTask DisposeAsync()
    {
        return _dbContainer.DisposeAsync();
    }
}
