﻿using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System.Text;

namespace PrePE.WebApi.Middlewares
{
    public class ValidateDtoAsyncActionFilter<T>(IValidator<T> validator) : IAsyncActionFilter
        where T : class
    {
        private readonly IValidator<T> _validator = validator;


        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var parameter = context.ActionArguments.FirstOrDefault(p => p.Value is T);
            if (parameter.Value != null)
            {
                var value = parameter.Value as T;
                var validationContext = new ValidationContext<T>(value!);
                var result = await _validator.ValidateAsync(validationContext);

                if (!result.IsValid)
                {
                    foreach (var failure in result.Errors)
                    {
                        context.ModelState.AddModelError(failure.PropertyName, failure.ErrorMessage);
                    }

                    var problem = new ProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = "Invalid arguments",
                        Detail = ParseToString(context.ModelState)
                    };

                    context.Result = new BadRequestObjectResult(problem);
                    return;
                }
            }

            await next();
        }


        static string ParseToString(ModelStateDictionary dict)
        {
            StringBuilder sb = new();
            foreach (var key in dict.Keys)
            {
                var errors = dict[key]!.Errors;
                foreach (var error in errors)
                {
                    sb.Append($"{error.ErrorMessage}；");
                }
            }
            return sb.ToString().TrimEnd('；');
        }
    }
}
