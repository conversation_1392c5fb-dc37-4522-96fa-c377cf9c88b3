﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Enums;

namespace PrePE.WebApi.Tests.AppointmentMng;

internal class NumberSourceFaker : BaseFaker<NumberSource>
{
    public NumberSourceFaker()
    {
        DataFaker = new Faker<NumberSource>()
            .RuleFor(x => x.ScheduleDate, faker => faker.Date.Recent(7))
            .RuleFor(x => x.BeginMinute, faker => faker.Random.Int(8 * 60, 10 * 60))
            .RuleFor(x => x.EndMinute, faker => faker.Random.Int(16 * 60, 18 * 60))
            .RuleFor(x => x.SourceType, faker => faker.PickRandom<NumberSourceType>())
            .RuleFor(x => x.TotalCount, faker => faker.Random.Int(10, 50))
            .RuleFor(x => x.BookedCount, faker => faker.Random.Int(0, 10))
            .RuleFor(x => x.IsRestDay, faker => false);
    }
}
