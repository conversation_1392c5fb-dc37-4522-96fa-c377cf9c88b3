﻿namespace PrePE.WebApi.Controllers;

[Route("api/[controller]")]
[ApiController]
public class BaseController : ControllerBase
{
    /// <summary>
    /// 返回400
    /// </summary>
    /// <param name="message"></param>
    /// <param name="title"></param>
    /// <returns></returns>
    protected BadRequestObjectResult Problem400Result(string message = "Bad request", string title = "Bad request")
    {
        return BadRequest(new ProblemDetails()
        {
            Status = StatusCodes.Status400BadRequest,
            Title = title,
            Detail = message
        });
    }

    /// <summary>
    /// 返回404
    /// </summary>
    /// <param name="message"></param>
    /// <param name="title"></param>
    /// <returns></returns>
    protected NotFoundObjectResult Problem404Result(string message = "Data not found", string title = "Not found")
    {
        return NotFound(new ProblemDetails()
        {
            Status = StatusCodes.Status404NotFound,
            Title = title,
            Detail = message
        });
    }
}
