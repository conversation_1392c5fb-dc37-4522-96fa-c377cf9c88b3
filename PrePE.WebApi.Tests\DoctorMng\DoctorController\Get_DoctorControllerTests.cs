﻿using PrePE.Domain.DoctorMng.Entities;
using PrePE.WebApi.Dtos.DoctorMng;
using Humanizer;

namespace PrePE.WebApi.Tests.DoctorMng.DoctorController
{
    [Collection(nameof(DatabaseCollection))]
    public class Get_DoctorControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        //private readonly string _token = JwtFixtures.GenerateToken4Doctor();
        private DoctorDto? _stubDoctor;


        [Fact]
        public async Task Get_返回实体_当信息存在时()
        {
            var client = _factory.CreateClient();

            var rst = await client.GetFromJsonAsync<DoctorDto>(Urls.Doctor.Get.FormatWith(_stubDoctor!.Id));

            rst.Should().NotBeNull();
            rst.Should().BeEquivalentTo(_stubDoctor);
        }

        [Fact]
        public async Task Get_返回404_当信息不存在时()
        {
            var client = _factory.CreateClient();

            var response = await client.GetAsync(Urls.Doctor.Get.FormatWith(++_stubDoctor!.Id));

            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }


        public async Task InitializeAsync()
        {
            var _faker = new Faker<CreateDoctorRequest>()
                .RuleFor(x => x.Name, faker => faker.Name.FullName())
                .RuleFor(x => x.UserName, faker => faker.Internet.UserName())
                .RuleFor(x => x.Password, faker => faker.Internet.Password())
                .RuleFor(x => x.AutographUrl, faker => faker.Internet.Url())
                .RuleFor(x => x.RoleId, faker => faker.Random.Number(1, 10))
            ;
            var req = _faker.Generate();
            var client = _factory.CreateClient(JwtFixtures.GenerateToken4Admin());

            var response = await client.PostAsJsonAsync(Urls.Doctor.Create, req);

            _stubDoctor = await response.Content.ReadFromJsonAsync<DoctorDto>();
        }

        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Doctor>();
        }
    }
}
