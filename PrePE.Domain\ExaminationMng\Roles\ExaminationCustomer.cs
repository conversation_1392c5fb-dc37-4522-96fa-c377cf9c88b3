using PrePE.Domain.CustomerMng.Entities;
using PrePE.Domain.ExaminationMng.Entities;
using PrePE.Domain.ExaminationMng.Enums;
using PrePE.Domain.ExaminationMng.Relations;

namespace PrePE.Domain.ExaminationMng.Roles;

/// <summary>
/// 体检客户角色
/// </summary>
/// <param name="customer">客户</param>
/// <param name="examinationForms">体检单关系</param>
public class ExaminationCustomer(Customer customer, IExaminationFormOfCustomer examinationForms)
{
    public Customer Customer { get; } = customer;
    public IExaminationFormOfCustomer ExaminationForms { get; } = examinationForms;

    /// <summary>
    /// 创建体检单
    /// </summary>
    /// <param name="appointmentId">预约ID</param>
    /// <param name="examinationDate">体检日期</param>
    /// <param name="examinationType">体检类型</param>
    /// <param name="examinationItems">体检项目列表</param>
    /// <returns>体检单</returns>
    public async Task<ExaminationForm> CreateExaminationFormAsync(
        string appointmentId,
        DateTime examinationDate,
        ExaminationType examinationType,
        List<ExaminationItem> examinationItems)
    {
        // 检查是否已存在该预约的体检单
        var existingForm = await ExaminationForms.GetByAppointmentIdAsync(appointmentId);
        if (existingForm != null)
        {
            throw new InvalidOperationException($"预约 {appointmentId} 的体检单已存在");
        }

        // 计算总费用
        var totalAmount = examinationItems.Sum(item => item.Price);

        // 创建体检单
        var examinationForm = new ExaminationForm
        {
            Id = ExaminationForm.GenerateId(),
            CustomerId = Customer.Id,
            CustomerName = Customer.Name,
            AppointmentId = appointmentId,
            ExaminationDate = examinationDate,
            ExaminationType = examinationType,
            Status = ExaminationStatus.Pending,
            TotalAmount = totalAmount,
            PaidAmount = 0,
            PaymentStatus = PaymentStatus.Unpaid,
            CreationTime = DateTime.Now
        };

        // 添加体检单
        await ExaminationForms.AddAsync(examinationForm);

        return examinationForm;
    }

    /// <summary>
    /// 获取体检单列表
    /// </summary>
    /// <returns>体检单列表</returns>
    public async Task<List<ExaminationForm>> GetExaminationFormsAsync()
    {
        return await ExaminationForms.GetAllAsync();
    }

    /// <summary>
    /// 根据预约ID获取体检单
    /// </summary>
    /// <param name="appointmentId">预约ID</param>
    /// <returns>体检单</returns>
    public async Task<ExaminationForm?> GetExaminationFormByAppointmentIdAsync(string appointmentId)
    {
        return await ExaminationForms.GetByAppointmentIdAsync(appointmentId);
    }
}
