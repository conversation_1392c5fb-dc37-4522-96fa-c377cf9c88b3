﻿using FluentValidation;

namespace PrePE.WebApi.Dtos.SystemMng
{
    public class AdminRegistRequestValidator : AbstractValidator<AdminRegistRequest>
    {
        public AdminRegistRequestValidator()
        {
            RuleFor(x => x.UserName).MinimumLength(5).WithMessage("UserName至少包含5个字符");
            RuleFor(x => x.Password).MinimumLength(8).WithMessage("Password至少包含为8个字符");
        }
    }
}
