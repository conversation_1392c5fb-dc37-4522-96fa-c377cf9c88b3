namespace PrePE.Domain.ExaminationMng.Enums;

/// <summary>
/// 体检类型
/// </summary>
public enum ExaminationType
{
    /// <summary>
    /// 个人体检
    /// </summary>
    Individual = 1,

    /// <summary>
    /// 团体体检
    /// </summary>
    Group = 2,

    /// <summary>
    /// 入职体检
    /// </summary>
    Employment = 3,

    /// <summary>
    /// 健康证体检
    /// </summary>
    HealthCertificate = 4,

    /// <summary>
    /// 驾驶证体检
    /// </summary>
    DriverLicense = 5,

    /// <summary>
    /// 专项体检
    /// </summary>
    Specialized = 6
}
