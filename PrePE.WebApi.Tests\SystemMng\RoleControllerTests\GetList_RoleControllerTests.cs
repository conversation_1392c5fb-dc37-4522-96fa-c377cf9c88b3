﻿using PrePE.Domain.SystemMng.Entities;
using PrePE.WebApi.Dtos.SystemMng;

namespace PrePE.WebApi.Tests.SystemMng.RoleControllerTests
{
    [Collection(nameof(DatabaseCollection))]
    public class GetList_RoleControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private readonly Faker<RoleDto> _faker = new Faker<RoleDto>()
            .RuleFor(x => x.Name, faker => faker.Commerce.Department())
            .RuleFor(x => x.MenuIds, faker => Enumerable.Range(0, 10)
                                                        .Select(_ => faker.Random.Int(1, 10))
                                                        .Distinct()
                                                        .ToArray())
        ;
        private readonly string _token = JwtFixtures.GenerateToken4Admin();


        [Fact]
        public async Task GetList_返回非空列表_当数据存在时()
        {
            var client = _factory.CreateClient(_token);

            for (int i = 0; i < 10; i++)
            {
                var role = _faker.Generate();
                await client.PostAsJsonAsync(Urls.Role.Create, role);
            }

            var roles = await client.GetFromJsonAsync<List<Role>>(Urls.Role.List);

            roles!.Count.Should().Be(10);

            await TestDataCleaner.CleanAsync<Role>();
        }

        [Fact]
        public async Task GetList_返回空列表_当数据不存在时()
        {
            var client = _factory.CreateClient();

            var roles = await client.GetFromJsonAsync<List<Role>>(Urls.Role.List);

            roles!.Count.Should().Be(0);
        }


        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public Task DisposeAsync()
        {
            return Task.CompletedTask;
        }
    }
}
