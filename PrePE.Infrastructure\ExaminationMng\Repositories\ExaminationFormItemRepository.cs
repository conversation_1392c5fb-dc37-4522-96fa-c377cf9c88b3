using PrePE.Domain.ExaminationMng.Entities;
using PrePE.Domain.ExaminationMng.Enums;
using PrePE.Domain.ExaminationMng.Repositories;

namespace PrePE.Infrastructure.ExaminationMng.Repositories;

/// <summary>
/// 体检单项目仓储实现
/// </summary>
public class ExaminationFormItemRepository(HealthDbContext dbContext)
    : BaseRepository<ExaminationFormItem>(dbContext), IExaminationFormItemRepository
{
    /// <summary>
    /// 根据体检单ID获取体检项目列表
    /// </summary>
    /// <param name="formId">体检单ID</param>
    /// <returns>体检项目列表</returns>
    public async Task<List<ExaminationFormItem>> GetByFormIdAsync(string formId)
    {
        return await DbContext.Db.Queryable<ExaminationFormItem>()
            .Where(x => x.FormId == formId)
            .OrderBy(x => x.ItemType)
            .ToListAsync();
    }

    /// <summary>
    /// 根据体检单ID和状态获取体检项目列表
    /// </summary>
    /// <param name="formId">体检单ID</param>
    /// <param name="status">项目状态</param>
    /// <returns>体检项目列表</returns>
    public async Task<List<ExaminationFormItem>> GetByFormIdAndStatusAsync(string formId, ExaminationItemStatus status)
    {
        return await DbContext.Db.Queryable<ExaminationFormItem>()
            .Where(x => x.FormId == formId && x.Status == status)
            .OrderBy(x => x.ItemType)
            .ToListAsync();
    }

    /// <summary>
    /// 批量添加体检项目
    /// </summary>
    /// <param name="formItems">体检项目列表</param>
    /// <returns></returns>
    public new async Task AddRangeAsync(List<ExaminationFormItem> formItems)
    {
        await DbContext.Db.Insertable(formItems).ExecuteCommandAsync();
    }
}
