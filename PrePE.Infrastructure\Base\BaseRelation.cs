﻿using System.Linq.Expressions;

namespace PrePE.Infrastructure.Base
{
    public abstract class BaseRelation<TMaster, TSlave>(HealthDbContext dbContext, TMaster master)
        : IBaseRelation<TMaster, TSlave>
        where TMaster : Entity, new()
        where TSlave : Entity, new()
    {
        public HealthDbContext DbContext { get; protected set; } = dbContext;
        public TMaster Master { get; protected set; } = master;



        public virtual async Task AddAsync(TSlave follower)
        {
            await DbContext.Db.Insertable(follower).ExecuteCommandAsync();
        }


        public virtual async Task<int> AddReturnIdentityAsync(TSlave follower)
        {
            return await DbContext.Db.Insertable(follower).ExecuteReturnIdentityAsync();
        }


        public virtual async Task<long> AddReturnBigIdentityAsync(TSlave follower)
        {
            return await DbContext.Db.Insertable(follower).ExecuteReturnBigIdentityAsync();
        }


        public virtual async Task AddRangeAsync(List<TSlave> followers)
        {
            await DbContext.Db.Insertable(followers).ExecuteCommandAsync();
        }


        public virtual async Task DeleteAsync(object id)
        {
            await DbContext.Db.Deleteable<TSlave>(id).ExecuteCommandAsync();
        }


        public virtual async Task UpdateAsync(TSlave follower)
        {
            await DbContext.Db.Updateable(follower).ExecuteCommandAsync();
        }


        public virtual async Task<TSlave> GetSingleAsync(Expression<Func<TSlave, bool>>? expression = null)
        {
            return await DbContext.Db.Queryable<TSlave>().Where(expression).SingleAsync();
        }


        public virtual async Task<List<TSlave>> GetListAsync(Expression<Func<TSlave, bool>>? expression = null)
        {
            return await DbContext.Db.Queryable<TSlave>().Where(expression).ToListAsync();
        }
    }
}
