using PrePE.Domain.ExaminationMng.Enums;

namespace PrePE.WebApi.Dtos.ExaminationMng;

/// <summary>
/// 体检单DTO
/// </summary>
public class MedicalExaminationFormDto
{
    /// <summary>
    /// 体检单ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 客户ID
    /// </summary>
    public long CustomerId { get; set; }

    /// <summary>
    /// 客户姓名
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 预约ID
    /// </summary>
    public string AppointmentId { get; set; } = string.Empty;

    /// <summary>
    /// 体检日期
    /// </summary>
    public DateTime ExaminationDate { get; set; }

    /// <summary>
    /// 体检状态
    /// </summary>
    public ExaminationStatus Status { get; set; }

    /// <summary>
    /// 体检类型
    /// </summary>
    public ExaminationType ExaminationType { get; set; }

    /// <summary>
    /// 总费用
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 已支付金额
    /// </summary>
    public decimal PaidAmount { get; set; }

    /// <summary>
    /// 支付状态
    /// </summary>
    public PaymentStatus PaymentStatus { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreationTime { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletionTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remarks { get; set; }

    /// <summary>
    /// 体检项目列表
    /// </summary>
    public List<ExaminationFormItemDto> Items { get; set; } = new();
}

/// <summary>
/// 体检单项目DTO
/// </summary>
public class ExaminationFormItemDto
{
    /// <summary>
    /// ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 体检单ID
    /// </summary>
    public string FormId { get; set; } = string.Empty;

    /// <summary>
    /// 体检项目ID
    /// </summary>
    public long ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string ItemCode { get; set; } = string.Empty;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// 项目类型
    /// </summary>
    public ExaminationItemType ItemType { get; set; }

    /// <summary>
    /// 项目价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 小计金额
    /// </summary>
    public decimal Subtotal { get; set; }

    /// <summary>
    /// 检查状态
    /// </summary>
    public ExaminationItemStatus Status { get; set; }

    /// <summary>
    /// 检查结果
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// 检查医生ID
    /// </summary>
    public long? DoctorId { get; set; }

    /// <summary>
    /// 检查医生姓名
    /// </summary>
    public string? DoctorName { get; set; }

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime? ExaminationTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remarks { get; set; }
}

/// <summary>
/// 创建体检单请求
/// </summary>
public class CreateMedicalExaminationFormRequest
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public long CustomerId { get; set; }

    /// <summary>
    /// 预约ID
    /// </summary>
    public string AppointmentId { get; set; } = string.Empty;

    /// <summary>
    /// 体检日期
    /// </summary>
    public DateTime ExaminationDate { get; set; }

    /// <summary>
    /// 体检类型
    /// </summary>
    public ExaminationType ExaminationType { get; set; }

    /// <summary>
    /// 体检项目ID列表
    /// </summary>
    public List<long> ExaminationItemIds { get; set; } = new();

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remarks { get; set; }
}

/// <summary>
/// 体检项目DTO
/// </summary>
public class ExaminationItemDto
{
    /// <summary>
    /// ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string ItemCode { get; set; } = string.Empty;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// 项目类型
    /// </summary>
    public ExaminationItemType ItemType { get; set; }

    /// <summary>
    /// 项目价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 检查部位
    /// </summary>
    public string? ExaminationPart { get; set; }

    /// <summary>
    /// 检查方法
    /// </summary>
    public string? ExaminationMethod { get; set; }

    /// <summary>
    /// 临床意义
    /// </summary>
    public string? ClinicalSignificance { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 排序号
    /// </summary>
    public int SortOrder { get; set; }
}
