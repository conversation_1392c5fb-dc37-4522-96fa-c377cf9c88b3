using PrePE.Domain.CustomerMng.Repositories;
using PrePE.Domain.ExaminationMng.Entities;
using PrePE.Domain.ExaminationMng.Repositories;
using PrePE.Infrastructure.CustomerMng;
using PrePE.WebApi.Dtos.ExaminationMng;
using PrePE.WebApi.Middlewares;

namespace PrePE.WebApi.Controllers.ExaminationMng;

/// <summary>
/// 体检单控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class MedicalExaminationFormController(
    IMapper mapper,
    IMedicalExaminationFormRepository medicalExaminationFormRepository,
    IExaminationItemRepository examinationItemRepository,
    IExaminationFormItemRepository examinationFormItemRepository,
    ICustomerRepository customerRepository,
    CustomerContext customerContext) : BaseController
{
    private readonly IMapper _mapper = mapper;
    private readonly IMedicalExaminationFormRepository _medicalExaminationFormRepository = medicalExaminationFormRepository;
    private readonly IExaminationItemRepository _examinationItemRepository = examinationItemRepository;
    private readonly IExaminationFormItemRepository _examinationFormItemRepository = examinationFormItemRepository;
    private readonly ICustomerRepository _customerRepository = customerRepository;
    private readonly CustomerContext _customerContext = customerContext;

    /// <summary>
    /// 创建体检单
    /// </summary>
    /// <param name="request">创建体检单请求</param>
    /// <returns>体检单</returns>
    [HttpPost("CreateExaminationForm")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<CreateMedicalExaminationFormRequest>))]
    public async Task<ActionResult<MedicalExaminationFormDto>> CreateExaminationForm([FromBody] CreateMedicalExaminationFormRequest request)
    {
        try
        {
            // 获取客户信息
            var customer = await _customerRepository.GetSingleAsync(x => x.Id == request.CustomerId);
            if (customer == null)
            {
                return NotFound(new { Message = $"客户 {request.CustomerId} 不存在" });
            }

            // 获取体检项目列表
            var examinationItems = new List<ExaminationItem>();
            foreach (var itemId in request.ExaminationItemIds)
            {
                var item = await _examinationItemRepository.GetSingleAsync(x => x.Id == itemId);
                if (item != null && item.IsEnabled)
                {
                    examinationItems.Add(item);
                }
            }

            if (examinationItems.Count == 0)
            {
                return BadRequest(new { Message = "没有有效的体检项目" });
            }

            // 将客户转换为体检客户角色
            var examinationCustomer = _customerContext.AsExaminationCustomer(customer);

            // 创建体检单
            var examinationForm = await examinationCustomer.CreateExaminationFormAsync(
                request.AppointmentId,
                request.ExaminationDate,
                request.ExaminationType,
                examinationItems);

            // 设置备注
            if (!string.IsNullOrEmpty(request.Remarks))
            {
                examinationForm.Remarks = request.Remarks;
                await _medicalExaminationFormRepository.UpdateAsync(examinationForm);
            }

            // 创建体检单项目
            var formItems = examinationItems.Select(item => new ExaminationFormItem
            {
                FormId = examinationForm.Id,
                ItemId = item.Id,
                ItemCode = item.ItemCode,
                ItemName = item.ItemName,
                ItemType = item.ItemType,
                Price = item.Price,
                Quantity = 1,
                Subtotal = item.Price,
                Status = Domain.ExaminationMng.Enums.ExaminationItemStatus.Pending,
                CreationTime = DateTime.Now
            }).ToList();

            await _examinationFormItemRepository.AddRangeAsync(formItems);

            // 返回体检单信息
            var result = _mapper.Map<MedicalExaminationFormDto>(examinationForm);
            result.Items = _mapper.Map<List<ExaminationFormItemDto>>(formItems);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new { Message = $"创建体检单失败: {ex.Message}" });
        }
    }

    /// <summary>
    /// 获取体检单详情
    /// </summary>
    /// <param name="id">体检单ID</param>
    /// <returns>体检单详情</returns>
    [HttpGet("GetExaminationForm")]
    public async Task<ActionResult<MedicalExaminationFormDto>> GetExaminationForm([FromQuery] string id)
    {
        var examinationForm = await _medicalExaminationFormRepository.GetSingleAsync(x => x.Id == id);
        if (examinationForm == null)
        {
            return NotFound(new { Message = $"体检单 {id} 不存在" });
        }

        var formItems = await _examinationFormItemRepository.GetByFormIdAsync(id);

        var result = _mapper.Map<MedicalExaminationFormDto>(examinationForm);
        result.Items = _mapper.Map<List<ExaminationFormItemDto>>(formItems);

        return Ok(result);
    }

    /// <summary>
    /// 获取客户的体检单列表
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <returns>体检单列表</returns>
    [HttpGet("GetCustomerExaminationForms")]
    public async Task<ActionResult<List<MedicalExaminationFormDto>>> GetCustomerExaminationForms([FromQuery] long customerId)
    {
        var customer = await _customerRepository.GetSingleAsync(x => x.Id == customerId);
        if (customer == null)
        {
            return NotFound(new { Message = $"客户 {customerId} 不存在" });
        }

        var examinationCustomer = _customerContext.AsExaminationCustomer(customer);

        var examinationForms = await examinationCustomer.GetExaminationFormsAsync();

        return Ok(_mapper.Map<List<MedicalExaminationFormDto>>(examinationForms));
    }

    /// <summary>
    /// 获取体检项目列表
    /// </summary>
    /// <returns>体检项目列表</returns>
    [HttpGet("GetExaminationItems")]
    public async Task<ActionResult<List<ExaminationItemDto>>> GetExaminationItems()
    {
        var items = await _examinationItemRepository.GetEnabledItemsAsync();
        return Ok(_mapper.Map<List<ExaminationItemDto>>(items));
    }
}
