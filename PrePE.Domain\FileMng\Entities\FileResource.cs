﻿using PrePE.Common.Utils;

namespace PrePE.Domain.FileMng.Entities
{
    [SugarTable("core_file_resource",TableDescription = "文件资源")]
    [SugarIndex("unique_{table}_file_name",nameof(FileName),OrderByType.Asc,true)]
    public class FileResource : Entity, IAggregateRoot
    {
        public static string RootPath = "FileResource";
        
        [SugarColumn(ColumnName = "id", IsNullable = false, IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        [SugarColumn(ColumnName = "file_name", Length  = 1000, ColumnDescription = "文件名")]
        public  string FileName { get; set; }

        /// <summary>
        /// 文件后缀名
        /// </summary>
        [SugarColumn(ColumnName = "file_suffix",  ColumnDescription = "文件后缀名")]
        public string  FileSuffix { get; set; }

        /// <summary>
        /// 文件相对路径
        /// </summary>
        [SugarColumn(ColumnName = "file_path",Length  = 1000, ColumnDescription = "文件相对路径")]
        public string  FilePath { get; set; }


        public override object GetIdentity()
        {
            return Id;
        }


        public static FileResource Create(string fileFullName,string userId = "")
        {
            var id = Guid.NewGuid();
            var directory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, FileResource.RootPath);
            FileUtil.CreateDirectoryIfNotExists(directory);
            var relativeFileLocation = RootPath;
            if (!userId.IsNullOrEmpty())
            {
                relativeFileLocation  = Path.Combine(RootPath, userId);
            }
            fileFullName = fileFullName.Replace("\n", "").Replace("\r", "").Trim();
            var fileSuffix = Path.GetExtension(fileFullName);
             var fileName = Path.GetFileName(fileFullName);
            if (fileSuffix.IsNullOrEmpty())
            {
                throw new ArgumentException("不支持无后缀名文件上传");
            }

            var newFileMaName = $"{id}{fileSuffix}";
            return new  FileResource()
            {
                Id = id,
                FileName = fileName,
                FilePath = Path.Combine(relativeFileLocation, newFileMaName),
                FileSuffix = fileSuffix
            };
        }
    }
}
