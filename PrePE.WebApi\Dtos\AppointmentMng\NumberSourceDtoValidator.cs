﻿using FluentValidation;
using PrePE.Domain.AppointmentMng.Enums;

namespace PrePE.WebApi.Dtos.AppointmentMng
{
    public class QueryNumberSourceRequestValidator : AbstractValidator<QueryNumberSourceRequest>
    {
        public QueryNumberSourceRequestValidator()
        {
            RuleFor(x => x.StartDate)
                .GreaterThan(new DateTime(2000, 1, 1)).WithMessage("开始日期不能早于2000-01-01");

            RuleFor(x => x.EndDate)
                .GreaterThan(new DateTime(2000, 1, 1)).WithMessage("结束日期不能早于2000-01-01")
                .GreaterThanOrEqualTo(x => x.StartDate).WithMessage("结束日期不能早于开始日期");
        }
    }

    /// <summary>
    /// 创建号源请求验证器
    /// </summary>
    public class CreateNumberSourceRequestValidator : AbstractValidator<CreateNumberSourceRequest>
    {
        public CreateNumberSourceRequestValidator()
        {
            RuleFor(x => x.ScheduleDate)
                .NotEmpty().WithMessage("排班日期不能为空")
                .GreaterThanOrEqualTo(DateTime.Today).WithMessage("排班日期不能早于今天");

            RuleFor(x => x.TimeSlotId)
                .GreaterThan(0).WithMessage("时间段ID必须大于0");

            RuleFor(x => x.SourceType)
                .IsInEnum().WithMessage("号源类型无效");

            RuleFor(x => x.TotalCount)
                .GreaterThan(0).WithMessage("总数量必须大于0");
        }
    }

    /// <summary>
    /// 批量创建号源请求验证器
    /// </summary>
    public class BatchCreateNumberSourceRequestValidator : AbstractValidator<BatchCreateNumberSourceRequest>
    {
        public BatchCreateNumberSourceRequestValidator()
        {
            RuleFor(x => x.BeginDate)
                .NotEmpty().WithMessage("排班日期不能为空")
                .GreaterThanOrEqualTo(DateTime.Today).WithMessage("排班日期不能早于今天");

            RuleFor(x => x.Items)
                .NotEmpty().WithMessage("号源列表不能为空");

            RuleForEach(x => x.Items).ChildRules(item =>
            {
                item.RuleFor(x => x.TotalCount)
                    .GreaterThan(0).WithMessage("总数量必须大于0");
            });
        }
    }

    /// <summary>
    /// 更新号源请求验证器
    /// </summary>
    public class UpdateNumberSourceRequestValidator : AbstractValidator<UpdateNumberSourceRequest>
    {
        public UpdateNumberSourceRequestValidator()
        {
            RuleFor(x => x.Id)
                .GreaterThan(0).WithMessage("ID必须大于0");
        }
    }

    /// <summary>
    /// 按日期范围批量创建号源请求验证器
    /// </summary>
    public class CreateNumberSourcesRequestValidator : AbstractValidator<CreateNumberSourcesRequest>
    {
        public CreateNumberSourcesRequestValidator()
        {
            RuleFor(x => x.StartDate)
                .NotEmpty().WithMessage("开始日期不能为空")
                .GreaterThanOrEqualTo(DateTime.Today).WithMessage("开始日期不能早于今天");

            RuleFor(x => x.EndDate)
                .NotEmpty().WithMessage("结束日期不能为空")
                .GreaterThanOrEqualTo(x => x.StartDate).WithMessage("结束日期不能早于开始日期");

            RuleFor(x => x.SourceType)
                .IsInEnum().WithMessage("号源类型无效");

            // 当号源类型为特殊项目时，特殊项目编码和名称不能为空
            When(x => x.SourceType == NumberSourceType.Special, () =>
            {
                RuleFor(x => x.SpecialItemCode)
                    .NotEmpty().WithMessage("特殊项目编码不能为空");

                RuleFor(x => x.SpecialItemName)
                    .NotEmpty().WithMessage("特殊项目名称不能为空");
            });

            RuleFor(x => x.Items)
                .NotEmpty().WithMessage("号源项列表不能为空");

            RuleForEach(x => x.Items).ChildRules(item =>
            {
                item.RuleFor(x => x.BeginMinute)
                    .GreaterThanOrEqualTo(0).WithMessage("开始分钟数必须大于等于0")
                    .LessThan(24 * 60).WithMessage("开始分钟数必须小于1440（24小时）");

                item.RuleFor(x => x.EndMinute)
                    .GreaterThan(x => x.BeginMinute).WithMessage("结束分钟数必须大于开始分钟数")
                    .LessThanOrEqualTo(24 * 60).WithMessage("结束分钟数必须小于等于1440（24小时）");

                item.RuleFor(x => x.TotalCount)
                    .GreaterThan(0).WithMessage("总数量必须大于0");
            });
        }
    }
}
