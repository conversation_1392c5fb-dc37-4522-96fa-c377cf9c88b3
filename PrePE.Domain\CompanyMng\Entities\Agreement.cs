using PrePE.Domain.CompanyMng.Enums;

namespace PrePE.Domain.CompanyMng.Entities;

/// <summary>
/// 体检协议
/// </summary>
public class Agreement : Entity
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    [SugarColumn(ColumnName = "serial_number", ColumnDescription = "序号")]
    public int SerialNumber { get; set; }

    ///// <summary>
    ///// 协议编号（系统生成）
    ///// </summary>
    //public string AgreementNumber { get; set; }

    /// <summary>
    /// 团检单位（公司）ID
    /// </summary>
    public int CompanyId { get; set; }

    ///// <summary>
    ///// 协议签订日期
    ///// </summary>
    //public DateTime SignDate { get; set; }

    /// <summary>
    /// 协议生效日期
    /// </summary>
    public DateTime EffectiveDate { get; set; }

    /// <summary>
    /// 协议终止日期
    /// </summary>
    public DateTime ExpirationDate { get; set; }

    /// <summary>
    /// 协议状态（草稿、生效、过期、终止等）
    /// </summary>
    public AgreementStatus Status { get; set; }

    ///// <summary>
    ///// 协议总金额
    ///// </summary>
    //public decimal TotalAmount { get; set; }

    /// <summary>
    /// 协议备注
    /// </summary>
    public string? Remarks { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    public DateTime CreationTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [SugarColumn(ColumnName = "update_time", ColumnDescription = "更新时间")]
    public DateTime UpdateTime { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }

    //// 导航属性
    //public virtual Company Company { get; set; }
    //public virtual ICollection<AgreementPackage> AgreementPackages { get; set; }
    //public virtual ICollection<ExaminationBatch> ExaminationBatches { get; set; }
    //public virtual ICollection<BillingRecord> BillingRecords { get; set; }
    //public virtual ICollection<SettlementRecord> SettlementRecords { get; set; }
}
