﻿#define USE_SQLSERVER

namespace PrePE.Domain.SystemMng.Entities;

[SugarTable("sys_role", TableDescription = "系统角色")]
public class Role : Entity, IAggregateRoot
{
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    [SugarColumn(ColumnName = "name", ColumnDescription = "名称")]
    public string Name { get; set; }

#if USE_SQLSERVER
    [SugarColumn(ColumnName = "menu_ids", IsJson = true, ColumnDescription = "菜单Ids")]
#else
    [SugarColumn(ColumnName = "menu_ids", ColumnDataType = "integer[]", IsArray = true, ColumnDescription = "菜单Ids")]
#endif
    public int[]? MenuIds { get; set; }

    //[SugarColumn(ColumnName = "is_admin", ColumnDescription = "是否是管理员")]
    //public bool IsAdmin { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}
