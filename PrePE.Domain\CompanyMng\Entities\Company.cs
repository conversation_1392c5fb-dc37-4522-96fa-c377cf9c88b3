﻿using PrePE.Domain.CompanyMng.Relations;

namespace PrePE.Domain.CompanyMng.Entities;

/// <summary>
/// 公司
/// </summary>
[SugarTable("sys_company", TableDescription = "公司")]
[SugarIndex("unique_{table}_company_code", nameof(CompanyCode), OrderByType.Asc, true)]
public class Company : Entity, IAggregateRoot
{
    /// <summary>
    /// 公司ID
    /// </summary>
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 公司编码
    /// </summary>
    [SugarColumn(ColumnName = "company_code", ColumnDescription = "公司编码")]
    public string CompanyCode { get; set; } = string.Empty;

    /// <summary>
    /// 公司名称
    /// </summary>
    [SugarColumn(ColumnName = "company_name", ColumnDescription = "公司名称")]
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// 公司简称
    /// </summary>
    [SugarColumn(ColumnName = "short_name", ColumnDescription = "公司简称")]
    public string? ShortName { get; set; }

    /// <summary>
    /// 父级公司ID
    /// </summary>
    [SugarColumn(ColumnName = "parent_id", ColumnDescription = "父级公司ID")]
    public long? ParentId { get; set; }

    /// <summary>
    /// 办公地址
    /// </summary>
    [SugarColumn(ColumnName = "office_address", ColumnDescription = "办公地址")]
    public string? OfficeAddress { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    [SugarColumn(ColumnName = "contacts", ColumnDescription = "联系人")]
    public string? Contacts { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    [SugarColumn(ColumnName = "phone", ColumnDescription = "联系电话")]
    public string? Phone { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    [SugarColumn(ColumnName = "email", ColumnDescription = "邮箱")]
    public string? Email { get; set; }

    /// <summary>
    /// 开户行
    /// </summary>
    [SugarColumn(ColumnName = "opening_bank", ColumnDescription = "开户行")]
    public string? OpeningBank { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>
    [SugarColumn(ColumnName = "bank_account_no", ColumnDescription = "银行账号")]
    public string? BankAccountNo { get; set; }


    /// <summary>
    /// 部门关系
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public IDepartmentOfCompany? Departments { get; set; }

    /// <summary>
    /// 协议关系
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public IAggreementOfCompany? Agreements { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}
