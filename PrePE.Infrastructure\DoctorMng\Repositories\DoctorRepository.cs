﻿using PrePE.Domain.DoctorMng.Entities;
using PrePE.Domain.DoctorMng.Repositories;
using PrePE.Domain.SystemMng;

namespace PrePE.Infrastructure.DoctorMng.Repositories
{
    public class DoctorRepository(HealthDbContext dbContext, IUserContext userContext)
        : BaseRepository<Doctor>(dbContext), IDoctorRepository
    {
        private IUserContext _userContext = userContext;

        public IUserContext InUserContext()
        {
            return _userContext;
        }
    }
}
