﻿namespace PrePE.Domain.AppointmentMng.Entities;

/// <summary>
/// 预约
/// </summary>
[SugarTable("appt_appointment", TableDescription = "预约")]
[SugarIndex("index_{table}_customer_id", nameof(CustomerId), OrderByType.Asc)]
public class Appointment : Entity, IAggregateRoot
{
    [SugarColumn(IsPrimaryKey = true)]
    public string Id { get; set; }

    /// <summary>
    /// 客户Id
    /// </summary>
    [SugarColumn(ColumnName = "customer_id", ColumnDescription = "客户Id")]
    public long CustomerId { get; set; }

    /// <summary>
    /// 客户姓名
    /// </summary>
    [SugarColumn(ColumnName = "customer_name", ColumnDescription = "客户姓名")]
    public string? CustomerName { get; set; }

    /// <summary>
    /// 预约日期
    /// </summary>
    [SugarColumn(ColumnName = "appointment_date", ColumnDescription = "预约日期")]
    public DateTime AppointmentDate { get; set; }

    /// <summary>
    /// 预约时间段ID
    /// </summary>
    [SugarColumn(ColumnName = "time_slot_id", ColumnDescription = "预约时间段ID")]
    public int TimeSlotId { get; set; }

    /// <summary>
    /// 预约状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "预约状态")]
    public int Status { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    public DateTime CreationTime { get; set; }

    /// <summary>
    /// 体检单Id
    /// </summary>
    [SugarColumn(ColumnName = "examination_form_id", ColumnDescription = "体检单Id")]
    public string? ExaminationFormId { get; set; }


    public override string ToString()
    {
        return $"{Id}";
    }


    public override object GetIdentity()
    {
        return Id;
    }


    public static string GenarateId()
    {
        //TODO: 实现生成预约ID
        return Guid.NewGuid().ToString();
    }
}
