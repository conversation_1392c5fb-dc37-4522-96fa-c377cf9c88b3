﻿namespace PrePE.Domain.CompanyMng.Entities;

/// <summary>
/// 部门（不是聚合根，通过关系对象模式聚合到公司）
/// </summary>
[SugarTable("sys_department", TableDescription = "部门")]
[SugarIndex("index_{table}_company_id", nameof(CompanyId), OrderByType.Asc)]
public class Department : Entity
{
    /// <summary>
    /// 部门ID
    /// </summary>
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 公司ID
    /// </summary>
    [SugarColumn(ColumnName = "company_id", ColumnDescription = "公司ID")]
    public long CompanyId { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "部门名称")]
    public string Name { get; set; } = string.Empty;


    public override object GetIdentity()
    {
        return Id;
    }
}
