﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Enums;
using SqlSugar;

namespace PrePE.CodeFirst.Initializer.SeedData.AppointmentMng;

public class NumberSourceTemplateDataInitializer : IDataInitializer
{
    public void Init(SqlSugarClient db)
    {
        try
        {
            int count = db.Insertable(GenerateData()).ExecuteCommand();
            Console.WriteLine($" appt_number_source_template 初始化了 {count} 条数据");
        }
        catch (Exception ex)
        {
            if (ex.Message.Contains("duplicate key value violates unique constraint"))
            {
                Console.WriteLine(" appt_number_source_template 已存在种子数据，跳过初始化");
            }
            else
            {
                Console.WriteLine($" appt_number_source_template 初始化发生错误: {ex.Message}");
            }
        }
    }

    public List<NumberSourceTemplate> GenerateData()
    {
        return
        [
            // 上午个检号源模板 (8:00-12:00)
            new NumberSourceTemplate
            {
                BeginMinute = 8 * 60,      // 8:00
                EndMinute = 12 * 60,       // 12:00
                SourceType = NumberSourceType.Individual,
                SpecialItemCode = null,
                SpecialItemName = null
            },
            
            // 下午个检号源模板 (14:00-18:00)
            new NumberSourceTemplate
            {
                BeginMinute = 14 * 60,     // 14:00
                EndMinute = 18 * 60,       // 18:00
                SourceType = NumberSourceType.Individual,
                SpecialItemCode = null,
                SpecialItemName = null
            },
            
            // 上午团检号源模板 (8:00-12:00)
            new NumberSourceTemplate
            {
                BeginMinute = 8 * 60,      // 8:00
                EndMinute = 12 * 60,       // 12:00
                SourceType = NumberSourceType.Group,
                SpecialItemCode = null,
                SpecialItemName = null
            },
            
            // 下午团检号源模板 (14:00-18:00)
            new NumberSourceTemplate
            {
                BeginMinute = 14 * 60,     // 14:00
                EndMinute = 18 * 60,       // 18:00
                SourceType = NumberSourceType.Group,
                SpecialItemCode = null,
                SpecialItemName = null
            },
            
            // 上午特殊项目号源模板 (8:00-12:00)
            new NumberSourceTemplate
            {
                BeginMinute = 8 * 60,      // 8:00
                EndMinute = 12 * 60,       // 12:00
                SourceType = NumberSourceType.Special,
                //SpecialItemCode = "",
                //SpecialItemName = ""
            },
            
            // 下午特殊项目号源模板 (14:00-18:00)
            new NumberSourceTemplate
            {
                BeginMinute = 14 * 60,     // 14:00
                EndMinute = 18 * 60,       // 18:00
                SourceType = NumberSourceType.Special,
                //SpecialItemCode = "",
                //SpecialItemName = ""
            }
        ];
    }
}
