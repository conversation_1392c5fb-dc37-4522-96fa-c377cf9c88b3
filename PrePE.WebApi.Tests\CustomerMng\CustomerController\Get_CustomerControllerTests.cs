﻿using PrePE.CodeFirst.Initializer;
using PrePE.Domain.CustomerMng.Entities;
using PrePE.WebApi.Dtos.CutomerMng;

namespace PrePE.WebApi.Tests.CustomerMng.CustomerController
{
    [Collection(nameof(DatabaseCollection))]
    public class Get_CustomerControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private readonly CustomerDtoFaker _faker = new();


        [Fact]
        public async Task Get_返回实体_当数据存在时()
        {
            var customer = _faker.Generate();
            var client = _factory.CreateClient();
            var response = await client.PostAsJsonAsync(Urls.Customer.Create, customer);
            var result = await response.Content.ReadFromJsonAsync<CustomerDto>();

            response = await client.GetAsync($"{Urls.Customer.Get}/?id={result!.Id}");

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var result2 = await response.Content.ReadFromJsonAsync<CustomerDto>();
            result2!.Id.Should().Be(result.Id);
            result2.Should().BeEquivalentTo(result2);
        }


        [Fact]
        public async Task Get_返回404_当数据不存在时()
        {
            var client = _factory.CreateClient();

            var response = await client.GetAsync($"{Urls.Customer.Get}/?id={999}");

            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Customer>();
        }
    }
}
