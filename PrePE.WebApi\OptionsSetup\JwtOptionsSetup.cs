﻿using PrePE.Common.Framework.Authentication;
using Microsoft.Extensions.Options;

namespace PrePE.WebApi.OptionsSetup
{
    public class JwtOptionsSetup(IConfiguration configuration) : IConfigureOptions<JwtOptions>
    {
        public const string SectionName = "Jwt";
        readonly IConfiguration _configuration = configuration;


        public void Configure(JwtOptions options)
        {
            _configuration.GetSection(SectionName).Bind(options);
        }
    }
}
