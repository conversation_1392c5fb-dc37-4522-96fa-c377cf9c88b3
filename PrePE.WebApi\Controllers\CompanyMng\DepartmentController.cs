
using PrePE.Domain.CompanyMng.Entities;
using PrePE.Domain.CompanyMng.Repositories;
using PrePE.Infrastructure;
using PrePE.Infrastructure.CompanyMng;
using PrePE.WebApi.Dtos.CompanyMng;
using PrePE.WebApi.Middlewares;

namespace PrePE.WebApi.Controllers.CompanyMng;

/// <summary>
/// 部门控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DepartmentController(
    IMapper mapper,
    ICompanyRepository companyRepository,
    CompanyContext companyContext) : BaseController
{
    private readonly IMapper _mapper = mapper;
    private readonly ICompanyRepository _companyRepository = companyRepository;
    private readonly CompanyContext _companyContext = companyContext;

    /// <summary>
    /// 创建部门
    /// </summary>
    /// <param name="request">创建部门请求</param>
    /// <returns>部门信息</returns>
    [HttpPost("CreateDepartment")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<CreateDepartmentRequest>))]
    public async Task<ActionResult<DepartmentDto>> CreateDepartment([FromBody] CreateDepartmentRequest request)
    {
        // 获取公司信息
        var company = await _companyRepository.GetSingleAsync(x => x.Id == request.CompanyId);
        if (company == null)
        {
            return NotFound(new { Message = $"公司 {request.CompanyId} 不存在" });
        }

        // 将公司转换为公司管理角色
        var companyManager = _companyContext.AsCompanyManager(company);

        // 创建部门
        var department = await companyManager.CreateDepartmentAsync(request.DepartmentName);

        return Ok(_mapper.Map<DepartmentDto>(department));
    }

    /// <summary>
    /// 更新部门
    /// </summary>
    /// <param name="request">更新部门请求</param>
    /// <returns>部门信息</returns>
    [HttpPut("UpdateDepartment")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<UpdateDepartmentRequest>))]
    public async Task<ActionResult<DepartmentDto>> UpdateDepartment([FromBody] UpdateDepartmentRequest request)
    {
        // 先获取部门信息以确定公司
        var department = await GetDepartmentWithCompanyAsync(request.Id);
        if (department == null)
        {
            return NotFound(new { Message = $"部门 {request.Id} 不存在" });
        }

        var company = await _companyRepository.GetSingleAsync(x => x.Id == department.CompanyId);
        if (company == null)
        {
            return NotFound(new { Message = $"公司 {department.CompanyId} 不存在" });
        }

        // 将公司转换为公司管理角色
        var companyManager = _companyContext.AsCompanyManager(company);

        // 更新部门
        await companyManager.UpdateDepartmentAsync(
            new Department
            {
                Id = request.Id,
                Name = request.DepartmentName!
            });

        return Ok();
    }

    /// <summary>
    /// 删除部门
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <returns>操作结果</returns>
    [HttpDelete("DeleteDepartment")]
    public async Task<ActionResult> DeleteDepartment([FromQuery] long id)
    {
        try
        {
            // 先获取部门信息以确定公司
            var department = await GetDepartmentWithCompanyAsync(id);
            if (department == null)
            {
                return NotFound(new { Message = $"部门 {id} 不存在" });
            }

            var company = await _companyRepository.GetSingleAsync(x => x.Id == department.CompanyId);
            if (company == null)
            {
                return NotFound(new { Message = $"公司 {department.CompanyId} 不存在" });
            }

            // 将公司转换为公司管理角色
            var companyManager = _companyContext.AsCompanyManager(company);

            // 删除部门
            await companyManager.DeleteDepartmentAsync(id);

            return Ok(new { Message = "部门删除成功" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Message = $"删除部门失败: {ex.Message}" });
        }
    }

    /// <summary>
    /// 获取部门详情
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <returns>部门详情</returns>
    [HttpGet("GetDepartment")]
    public async Task<ActionResult<DepartmentDto>> GetDepartment([FromQuery] long id)
    {
        var department = await GetDepartmentWithCompanyAsync(id);
        if (department == null)
        {
            return NotFound(new { Message = $"部门 {id} 不存在" });
        }

        return Ok(_mapper.Map<DepartmentDto>(department));
    }

    /// <summary>
    /// 获取公司的部门列表
    /// </summary>
    /// <param name="companyId">公司ID</param>
    /// <returns>部门列表</returns>
    [HttpGet("GetDepartments")]
    public async Task<ActionResult<List<DepartmentDto>>> GetDepartments([FromQuery] long companyId)
    {
        // 获取公司信息
        var company = await _companyRepository.GetSingleAsync(x => x.Id == companyId);
        if (company == null)
        {
            return NotFound(new { Message = $"公司 {companyId} 不存在" });
        }

        // 将公司转换为公司管理角色
        var companyManager = _companyContext.AsCompanyManager(company);

        // 获取部门列表
        var departments = await companyManager.GetDepartmentsAsync();

        return Ok(_mapper.Map<List<DepartmentDto>>(departments));
    }

    /// <summary>
    /// 获取部门信息（包含公司信息）
    /// </summary>
    /// <param name="departmentId">部门ID</param>
    /// <returns>部门信息</returns>
    private async Task<Domain.CompanyMng.Entities.Department?> GetDepartmentWithCompanyAsync(long departmentId)
    {
        // 由于部门不是聚合根，需要通过数据库直接查询
        // 这里可以考虑在 CompanyContext 中添加一个辅助方法
        var dbContext = HttpContext.RequestServices.GetRequiredService<HealthDbContext>();
        return await dbContext.Db.Queryable<Domain.CompanyMng.Entities.Department>()
            .FirstAsync(x => x.Id == departmentId);
    }
}
