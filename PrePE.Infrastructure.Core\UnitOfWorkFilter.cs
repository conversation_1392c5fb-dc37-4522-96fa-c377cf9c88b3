﻿using Microsoft.AspNetCore.Mvc.Filters;

namespace PrePE.Infrastructure.Core
{
    public class UnitOfWorkFilter(ISqlSugarClient db) : IActionFilter
    {
        readonly ISqlSugarClient _db = db;


        public void OnActionExecuting(ActionExecutingContext context)
        {
            _db.Ado.BeginTran();
        }

        public void OnActionExecuted(ActionExecutedContext context)
        {
            if (context.Exception == null)
            {
                _db.Ado.CommitTran();
            }
            else
            {
                _db.Ado.RollbackTran();
            }
        }
    }
}
