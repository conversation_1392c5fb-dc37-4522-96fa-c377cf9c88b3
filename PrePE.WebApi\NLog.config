﻿<?xml version="1.0" encoding="utf-8"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
	  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	  autoReload="true"
	  throwConfigExceptions="true"
	  internalLogLevel="Off"
	  internalLogFile="${basedir}/Logs/internal-nlog/internal-nlog-AspNetCore.txt">

	<extensions>
		<add assembly="NLog.Web.AspNetCore"/>
	</extensions>

	<variable name="connectionString" value="${configsetting:name=ConnectionStrings.PrePE}"/>

	<targets>
		<!--Error保存至文件-->
		<target name="error_file" xsi:type="File" maxArchiveFiles="30"  encoding="utf-8"
                fileName="${basedir}/Logs/${date:yyyyMMdd}_Error.log"
                archiveFileName="${basedir}/Logs/${date:yyyyMMdd}_Error.{#}.log"
                archiveDateFormat="yyyyMMdd"
                archiveAboveSize="104857600"
                archiveNumbering="Sequence"
                layout="${date:yyyy-MM-dd HH\:mm\:ss} ${message} ${onexception:${exception:format=tostring} ${newline} ${stacktrace}" />
		<!--Info保存至文件-->
		<target name="info_file" xsi:type="File" maxArchiveFiles="30" encoding="utf-8"
                fileName="${basedir}/Logs/${date:yyyyMMdd}_Info.log"
                archiveFileName="${basedir}/Logs/${date:yyyyMMdd}_Info.{#}.log"
                archiveDateFormat="yyyyMMdd"
                archiveAboveSize="104857600"
                archiveNumbering="Sequence"
                layout="${date:yyyy-MM-dd HH\:mm\:ss} ${uppercase:${level}}： ${message}" />
		<!--配置Sql Server-->
		<target xsi:type="Database" name="database"
            connectionString="${var:connectionString}"
            commandText="INSERT INTO nlog (TimeStamp, Level, Logger, Message, Exception, Properties)
                         VALUES (@TimeStamp, @Level, @Logger, @Message, @Exception, @Properties)">
			<parameter name="@TimeStamp" layout="${date}" />
			<parameter name="@Level" layout="${level:uppercase=true}" />
			<parameter name="@Logger" layout="${logger}" />
			<parameter name="@Message" layout="${message}" />
			<parameter name="@Exception" layout="${exception:format=tostring}" />
			<parameter name="@Properties" layout="${all-event-properties:separator=|}" />
		</target>
		<target name="console" xsi:type="Console" layout="${longdate} ${level} ${message} ${exception}" />
	</targets>
	<rules>
		<!-- add your logging rules here -->
		<logger name="*" minlevel="Info" maxlevel="Warn" writeTo="info_file" />
		<!--<logger name="*" minlevel="Debug" writeTo="debugger" />-->
		<logger name="*" minlevel="Error" writeTo="error_file" />
		<!--配置Sql Server-->
		<logger name="PrePE.WebApi.Middlewares.LoggingMiddleware" minlevel="Info" writeTo="database" />
		<logger name="*" minlevel="Info" writeTo="console" />
	</rules>
</nlog>