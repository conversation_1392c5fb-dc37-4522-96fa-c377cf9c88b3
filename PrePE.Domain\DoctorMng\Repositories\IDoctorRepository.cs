﻿using PrePE.Domain.DoctorMng.Entities;
using PrePE.Domain.SystemMng;

namespace PrePE.Domain.DoctorMng.Repositories;

public interface IDoctorRepository : IBaseRepository<Doctor>
{
    IUserContext InUserContext();

    //IManagementContext InManagementContext();
}


//public static class ManagementContextExtensions
//{
//    public static IManagementContext InManagementContext(this IUserContext context)
//    {
//        return new ManagementContext(context.DbContext, context.User);
//    }
//}