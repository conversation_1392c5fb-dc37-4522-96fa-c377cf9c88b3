using AutoMapper;
using PrePE.Domain.CompanyMng.Entities;
using PrePE.WebApi.Dtos.CompanyMng;

namespace PrePE.WebApi.AutoMapperProfiles.CompanyMng;

/// <summary>
/// 公司 AutoMapper 配置
/// </summary>
public class CompanyProfile : Profile
{
    public CompanyProfile()
    {
        CreateMap<Company, CompanyDto>().ReverseMap();
        CreateMap<Department, DepartmentDto>().ReverseMap();
        
        CreateMap<CreateCompanyRequest, Company>();
        CreateMap<CreateDepartmentRequest, Department>().ReverseMap();
    }
}
