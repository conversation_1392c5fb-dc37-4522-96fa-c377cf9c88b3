namespace PrePE.WebApi.Dtos.CompanyMng;

/// <summary>
/// 公司DTO
/// </summary>
public class CompanyDto
{
    /// <summary>
    /// 公司ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 公司编码
    /// </summary>
    public string CompanyCode { get; set; } = string.Empty;

    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// 公司简称
    /// </summary>
    public string? ShortName { get; set; }

    /// <summary>
    /// 统一社会信用代码
    /// </summary>
    public string? CreditCode { get; set; }

    /// <summary>
    /// 法定代表人
    /// </summary>
    public string? LegalRepresentative { get; set; }

    /// <summary>
    /// 注册资本
    /// </summary>
    public decimal? RegisteredCapital { get; set; }

    /// <summary>
    /// 成立日期
    /// </summary>
    public DateTime? EstablishmentDate { get; set; }

    /// <summary>
    /// 注册地址
    /// </summary>
    public string? RegisteredAddress { get; set; }

    /// <summary>
    /// 办公地址
    /// </summary>
    public string? OfficeAddress { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreationTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
}

/// <summary>
/// 创建公司请求
/// </summary>
public class CreateCompanyRequest
{
    /// <summary>
    /// 公司编码
    /// </summary>
    public string CompanyCode { get; set; } = string.Empty;

    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// 公司简称
    /// </summary>
    public string? ShortName { get; set; }

    /// <summary>
    /// 统一社会信用代码
    /// </summary>
    public string? CreditCode { get; set; }

    /// <summary>
    /// 法定代表人
    /// </summary>
    public string? LegalRepresentative { get; set; }

    /// <summary>
    /// 注册资本
    /// </summary>
    public decimal? RegisteredCapital { get; set; }

    /// <summary>
    /// 成立日期
    /// </summary>
    public DateTime? EstablishmentDate { get; set; }

    /// <summary>
    /// 注册地址
    /// </summary>
    public string? RegisteredAddress { get; set; }

    /// <summary>
    /// 办公地址
    /// </summary>
    public string? OfficeAddress { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 网站
    /// </summary>
    public string? Website { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// 更新公司请求
/// </summary>
public class UpdateCompanyRequest
{
    /// <summary>
    /// 公司ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 公司简称
    /// </summary>
    public string? ShortName { get; set; }

    /// <summary>
    /// 法定代表人
    /// </summary>
    public string? LegalRepresentative { get; set; }

    /// <summary>
    /// 注册资本
    /// </summary>
    public decimal? RegisteredCapital { get; set; }

    /// <summary>
    /// 成立日期
    /// </summary>
    public DateTime? EstablishmentDate { get; set; }

    /// <summary>
    /// 注册地址
    /// </summary>
    public string? RegisteredAddress { get; set; }

    /// <summary>
    /// 办公地址
    /// </summary>
    public string? OfficeAddress { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }
}
