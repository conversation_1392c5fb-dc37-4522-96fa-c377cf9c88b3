﻿using PrePE.Domain.SystemMng.Entities;
using PrePE.WebApi.Dtos.SystemMng;

namespace PrePE.WebApi.Tests.SystemMng.RoleControllerTests
{
    [Collection(nameof(DatabaseCollection))]
    public class Create_RoleControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private readonly Faker<RoleDto> _faker = new Faker<RoleDto>()
            .RuleFor(x => x.Name, faker => faker.Name.FirstName())
        ;
        private readonly string _token = JwtFixtures.GenerateToken4Admin();

        [Fact]
        public async Task Create_返回创建实体_当数据有效时()
        {
            var role = _faker.Generate();
            role.MenuIds = [1];
            var client = _factory.CreateClient(_token);

            var response = await client.PostAsJsonAsync(Urls.Role.Create, role);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var result = await response.Content.ReadFromJsonAsync<RoleDto>();
            result!.Id.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task Create_返回400_当名称为空时()
        {
            var role = _faker.Generate();
            role.Name = null;
            var client = _factory.CreateClient(_token);

            var response = await client.PostAsJsonAsync(Urls.Role.Create, role);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            string msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain("Name不能为空");
        }


        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Role>();
        }
    }
}
