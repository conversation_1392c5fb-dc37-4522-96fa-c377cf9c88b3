﻿using PrePE.WebApi.Dtos.CutomerMng;

namespace PrePE.WebApi.Tests.Fakers
{
    internal class CustomerDtoFaker : BaseFaker<CustomerDto>
    {
        public CustomerDtoFaker()
        {
            DataFaker = new Faker<CustomerDto>()
                .RuleFor(x => x.Name, faker => faker.Name.FirstName())
                .RuleFor(x => x.Birthday, faker => faker.Person.DateOfBirth)
                .RuleFor(x => x.PhoneNo, faker => faker.Random.String(11, '0', '9'))
                .RuleFor(x => x.Sex, faker => 1)
                .RuleFor(x => x.CardNo, faker => faker.Random.String(18, '0', '9'))
                .RuleFor(x => x.CardType, faker => 1)
            ;
        }
    }
}
