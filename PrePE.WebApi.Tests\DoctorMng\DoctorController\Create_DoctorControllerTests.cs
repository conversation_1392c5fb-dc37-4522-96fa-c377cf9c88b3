﻿using PrePE.Domain.DoctorMng.Entities;
using PrePE.WebApi.Dtos.DoctorMng;

namespace PrePE.WebApi.Tests.DoctorMng.DoctorController
{
    [Collection(nameof(DatabaseCollection))]
    public class Create_DoctorControllerTests(CustomWebApplicationFactory factory)
         : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private static readonly Faker<CreateDoctorRequest> _faker = new Faker<CreateDoctorRequest>()
            .RuleFor(x => x.Name, faker => faker.Name.FullName())
            .RuleFor(x => x.UserName, faker => faker.Internet.UserName())
            .RuleFor(x => x.Password, faker => faker.Internet.Password())
            .RuleFor(x => x.AutographUrl, faker => faker.Internet.Url())
            .RuleFor(x => x.RoleId, faker => faker.Random.Number(1, 10))
        ;
        private readonly string _token = JwtFixtures.GenerateToken4Admin();


        [Theory]
        [MemberData(nameof(GetValidData))]
        public async Task Create_返回200_当数据有效时(CreateDoctorRequest dto)
        {
            var client = _factory.CreateClient(_token);

            var response = await client.PostAsJsonAsync(Urls.Doctor.Create, dto);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }


        [Theory]
        [MemberData(nameof(GetInvalidData))]
        public async Task Create_返回400_当数据无效时(CreateDoctorRequest dto)
        {
            var client = _factory.CreateClient(_token);

            var response = await client.PostAsJsonAsync(Urls.Doctor.Create, dto);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }


        [Fact]
        public async Task Create_返回400_当用户名重复时()
        {
            var doctor = _faker.Generate();
            var client = _factory.CreateClient(_token);
            var response = await client.PostAsJsonAsync(Urls.Doctor.Create, doctor);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            response = await client.PostAsJsonAsync(Urls.Doctor.Create, doctor);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            string msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain("用户名已存在");
        }



        public static IEnumerable<object[]> GetValidData()
        {
            yield return [_faker.Generate()];

            var noRole = _faker.Generate();
            noRole.RoleId = 0;
            yield return [noRole];

            var noAutographUrl = _faker.Generate();
            noAutographUrl.AutographUrl = null;
            yield return [noAutographUrl];
        }

        public static IEnumerable<object[]> GetInvalidData()
        {
            var noUserName = _faker.Generate();
            noUserName.UserName = "";
            yield return [noUserName];

            var noPwd = _faker.Generate();
            noPwd.Password = "123";
            yield return [noPwd];

            var noName = _faker.Generate();
            noName.Name = "";
            yield return [noName];
        }


        public Task DisposeAsync()
        {
            return Task.CompletedTask;
        }

        public async Task InitializeAsync()
        {
            await TestDataCleaner.CleanAsync<Doctor>();
        }
    }
}
