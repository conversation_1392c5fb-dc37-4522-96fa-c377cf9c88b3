﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Repositories;

namespace PrePE.Infrastructure.AppointmentMng.Repositories
{
    public class NumberSourceTemplateRepository(HealthDbContext dbContext)
        : BaseRepository<NumberSourceTemplate>(dbContext), INumberSourceTemplateRepository
    {
        public async Task<List<NumberSourceTemplate>> GetAllTemplatesAsync()
        {
            return await DbContext.Db.Queryable<NumberSourceTemplate>().ToListAsync();
        }

        //public async Task<NumberSourceTemplate?> GetTemplateByIdAsync(long id)
        //{
        //    return await DbContext.Db.Queryable<NumberSourceTemplate>().FirstAsync(x => x.Id == id);
        //}

        //public async Task<long> AddTemplateAsync(NumberSourceTemplate template)
        //{
        //    return await DbContext.Db.Insertable(template).ExecuteReturnBigIdentityAsync();
        //}

        //public async Task<bool> UpdateTemplateAsync(NumberSourceTemplate template)
        //{
        //    return await DbContext.Db.Updateable(template).ExecuteCommandAsync() > 0;
        //}

        //public async Task<bool> DeleteTemplateAsync(long id)
        //{
        //    return await DbContext.Db.Deleteable<NumberSourceTemplate>().Where(x => x.Id == id).ExecuteCommandAsync() > 0;
        //}
    }
}
