using PrePE.Domain.ExaminationMng.Entities;
using PrePE.Domain.ExaminationMng.Enums;
using PrePE.Domain.ExaminationMng.Repositories;

namespace PrePE.Infrastructure.ExaminationMng.Repositories;

/// <summary>
/// 体检项目仓储实现
/// </summary>
public class ExaminationItemRepository(HealthDbContext dbContext)
    : BaseRepository<ExaminationItem>(dbContext), IExaminationItemRepository
{
    /// <summary>
    /// 根据项目编码获取体检项目
    /// </summary>
    /// <param name="itemCode">项目编码</param>
    /// <returns>体检项目</returns>
    public async Task<ExaminationItem?> GetByItemCodeAsync(string itemCode)
    {
        return await DbContext.Db.Queryable<ExaminationItem>()
            .FirstAsync(x => x.ItemCode == itemCode);
    }

    /// <summary>
    /// 根据项目类型获取体检项目列表
    /// </summary>
    /// <param name="itemType">项目类型</param>
    /// <returns>体检项目列表</returns>
    public async Task<List<ExaminationItem>> GetByItemTypeAsync(ExaminationItemType itemType)
    {
        return await DbContext.Db.Queryable<ExaminationItem>()
            .Where(x => x.ItemType == itemType && x.IsEnabled)
            .OrderBy(x => x.SortOrder)
            .ToListAsync();
    }

    /// <summary>
    /// 获取启用的体检项目列表
    /// </summary>
    /// <returns>体检项目列表</returns>
    public async Task<List<ExaminationItem>> GetEnabledItemsAsync()
    {
        return await DbContext.Db.Queryable<ExaminationItem>()
            .Where(x => x.IsEnabled)
            .OrderBy(x => x.SortOrder)
            .ToListAsync();
    }
}
