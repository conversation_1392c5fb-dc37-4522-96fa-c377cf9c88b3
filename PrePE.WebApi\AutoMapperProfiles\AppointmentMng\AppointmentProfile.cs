﻿﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.WebApi.Dtos.AppointmentMng;

namespace PrePE.WebApi.AutoMapperProfiles.AppointmentMng
{
    public class AppointmentProfile : Profile
    {
        public AppointmentProfile()
        {
            CreateMap<Appointment, AppointmentDto>().ReverseMap();
            CreateMap<CreateAppointmentRequest, Appointment>()
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => 0))
                .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.Now));
        }
    }
}
