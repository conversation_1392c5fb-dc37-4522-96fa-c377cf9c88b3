﻿using System.Linq.Expressions;
using PrePE.Domain.DoctorMng.Entities;
using PrePE.Domain.SystemMng.Entities;
using PrePE.Domain.SystemMng.Relations;

namespace PrePE.Infrastructure.SystemMng.Relations
{
    public class RoleOfUser(HealthDbContext db<PERSON><PERSON><PERSON><PERSON>, Doctor master)
        : BaseRelation<Doctor, Role>(db<PERSON><PERSON><PERSON><PERSON>, master), IRoleOfUser
    {
        public override async Task<Role> GetSingleAsync(Expression<Func<Role, bool>>? expression = null)
        {
            return await DbContext.Roles.AsQueryable()
                .WhereIF(expression is not null, expression)
                .Where(r => r.Id == Master.RoleId)
                .SingleAsync();
        }
    }
}
