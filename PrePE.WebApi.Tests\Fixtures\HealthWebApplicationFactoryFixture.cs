﻿using PrePE.CodeFirst.Initializer;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using SqlSugar;

namespace PrePE.WebApi.Tests.Fixtures
{
    //public class HealthWebApplicationFactoryFixture : IAsyncLifetime
    //{
    //    private readonly WebApplicationFactory<Program> _factory;
    //    public HttpClient Client { get; private set; }

    //    public HealthWebApplicationFactoryFixture()
    //    {
    //        _factory = new WebApplicationFactory<Program>().WithWebHostBuilder(builder =>
    //        {
    //            builder.ConfigureTestServices(services =>
    //            {
    //                var descriptor = services.SingleOrDefault(
    //                    d => d.ServiceType == typeof(ISqlSugarClient));
    //                if (descriptor != null)
    //                {
    //                    services.Remove(descriptor);
    //                }

    //                services.AddScoped<ISqlSugarClient>(p =>
    //                {
    //                    return new SqlSugarClient(
    //                        new ConnectionConfig()
    //                        {
    //                            DbType = DbConfig.GetDbType(),
    //                            ConnectionString = DbConfig.GetConnectionString(),
    //                            IsAutoCloseConnection = true,
    //                        }
    //                    );
    //                });
    //            });
    //        });

    //        Client = _factory.CreateClient();
    //    }


    //    public Task InitializeAsync()
    //    {
    //        DbInitializer initializer = new(DbConfig.GetConnectionString(), DbConfig.GetDbType());
    //        initializer.Init();

    //        return Task.CompletedTask;
    //    }

    //    public Task DisposeAsync()
    //    {
    //        DbCleaner cleaner = new(DbConfig.GetConnectionString(), DbConfig.GetDbType());
    //        cleaner.Clean();

    //        return Task.CompletedTask;
    //    }
    //}
}
