﻿using PrePE.Domain.AppointmentMng.Enums;

namespace PrePE.Domain.AppointmentMng.Entities;

/// <summary>
/// 号源
/// </summary>
[SugarTable("appt_number_source", TableDescription = "号源")]
public class NumberSource : Entity, IAggregateRoot
{
    /// <summary>
    /// ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 排班日期
    /// </summary>
    [SugarColumn(ColumnName = "schedule_date", ColumnDescription = "排班日期")]
    public DateTime ScheduleDate { get; set; }

    /// <summary>
    /// 开始分钟数
    /// </summary>
    [SugarColumn(ColumnName = "begin_minute", ColumnDescription = "开始分钟数")]
    public int BeginMinute { get; set; }

    /// <summary>
    /// 结束分钟数
    /// </summary>
    [SugarColumn(ColumnName = "end_minute", ColumnDescription = "结束分钟数")]
    public int EndMinute { get; set; }

    /// <summary>
    /// 号源类型
    /// </summary>
    [SugarColumn(ColumnName = "source_type", ColumnDescription = "号源类型")]
    public NumberSourceType SourceType { get; set; }

    /// <summary>
    /// 特殊号源项目编码
    /// </summary>
    [SugarColumn(ColumnName = "special_item_code", ColumnDescription = "特殊号源项目编码")]
    public string? SpecialItemCode { get; set; }

    /// <summary>
    /// 特殊号源项目名称
    /// </summary>
    [SugarColumn(ColumnName = "special_item_name", ColumnDescription = "特殊号源项目名称")]
    public string? SpecialItemName { get; set; }

    /// <summary>
    /// 总数量
    /// </summary>
    [SugarColumn(ColumnName = "total_count", ColumnDescription = "总数量")]
    public int TotalCount { get; set; }

    /// <summary>
    /// 已预约数量
    /// </summary>
    [SugarColumn(ColumnName = "booked_count", ColumnDescription = "已预约数量")]
    public int BookedCount { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    public DateTime? CreationTime { get; set; }

    ///// <summary>
    ///// 创建人ID
    ///// </summary>
    //[SugarColumn(ColumnName = "creator_id", ColumnDescription = "创建人ID")]
    //public long CreatorId { get; set; }

    /// <summary>
    /// 是否休假
    /// </summary>
    [SugarColumn(ColumnName = "is_rest_day", ColumnDescription = "是否休假")]
    public bool IsRestDay { get; set; }

    ///// <summary>
    ///// 备注
    ///// </summary>
    //[SugarColumn(ColumnName = "remark", ColumnDescription = "备注")]
    //public string? Remark { get; set; }

    /// <summary>
    /// 获取剩余数量
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public int RemainingCount => TotalCount - BookedCount;



    /// <summary>
    /// 检查是否还有剩余号源
    /// </summary>
    /// <returns>如果有剩余号源返回true，否则返回false</returns>
    public bool HasRemaining()
    {
        return !IsRestDay && RemainingCount > 0;
    }

    /// <summary>
    /// 预约一个号源
    /// </summary>
    /// <exception cref="InvalidOperationException">如果没有剩余号源或号源不可用</exception>
    public void Book()
    {
        if (!HasRemaining())
            throw new InvalidOperationException("没有可用的号源");

        BookedCount++;
    }

    /// <summary>
    /// 取消一个号源的预约
    /// </summary>
    /// <exception cref="InvalidOperationException">如果没有已预约的号源</exception>
    public void CancelBooking()
    {
        if (BookedCount <= 0)
            throw new InvalidOperationException("没有已预约的号源可取消");

        BookedCount--;
    }

    public override object GetIdentity()
    {
        return Id;
    }
}
