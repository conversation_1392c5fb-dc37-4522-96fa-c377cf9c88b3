﻿using PrePE.Domain.SystemMng.Entities;
using PrePE.Domain.SystemMng.Repositories;
using PrePE.WebApi.Dtos.SystemMng;
using PrePE.WebApi.Middlewares;
using Microsoft.AspNetCore.Authorization;

namespace PrePE.WebApi.Controllers.SystemMng;

[Authorize]
public class RoleController(IMapper mapper, IRoleRepository roleRepository)
    : BaseController
{
    private readonly IMapper _mapper = mapper;
    private readonly IRoleRepository _roleRepository = roleRepository;


    /// <summary>
    /// 创建角色
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost("create")]
    //[Authorize(Policy = AuthorizePolicies.Admin)]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<RoleDto>))]
    public async Task<ActionResult<RoleDto>> Create([FromBody] RoleDto dto)
    {
        var role = _mapper.Map<Role>(dto);
        role!.Id = await _roleRepository.AddReturnIdentityAsync(role);

        return Ok(_mapper.Map<RoleDto>(role));
    }

    /// <summary>
    /// 更新角色
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost("update")]
    //[Authorize(Policy = AuthorizePolicies.Admin)]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<RoleDto>))]
    public async Task<ActionResult> Update([FromBody] RoleDto dto)
    {
        var role = _mapper.Map<Role>(dto);
        await _roleRepository.UpdateAsync(role);

        return Ok();
    }

    /// <summary>
    /// 获取角色列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("list")]
    public async Task<ActionResult<RoleDto>> GetList()
    {
        var roles = await _roleRepository.GetListAsync();

        return Ok(_mapper.Map<List<RoleDto>>(roles));
    }
}
