using PrePE.Domain.ExaminationMng.Enums;

namespace PrePE.Domain.ExaminationMng.Entities;

/// <summary>
/// 体检项目
/// </summary>
[SugarTable("exam_examination_item", TableDescription = "体检项目")]
[SugarIndex("unique_{table}_item_code", nameof(ItemCode), OrderByType.Asc, true)]
public class ExaminationItem : Entity, IAggregateRoot
{
    /// <summary>
    /// ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    [SugarColumn(ColumnName = "item_code", ColumnDescription = "项目编码")]
    public string ItemCode { get; set; } = string.Empty;

    /// <summary>
    /// 项目名称
    /// </summary>
    [SugarColumn(ColumnName = "item_name", ColumnDescription = "项目名称")]
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// 项目类型
    /// </summary>
    [SugarColumn(ColumnName = "item_type", ColumnDescription = "项目类型")]
    public ExaminationItemType ItemType { get; set; }

    /// <summary>
    /// 项目价格
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "项目价格")]
    public decimal Price { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    [SugarColumn(ColumnName = "description", ColumnDescription = "项目描述")]
    public string? Description { get; set; }

    /// <summary>
    /// 检查部位
    /// </summary>
    [SugarColumn(ColumnName = "examination_part", ColumnDescription = "检查部位")]
    public string? ExaminationPart { get; set; }

    /// <summary>
    /// 检查方法
    /// </summary>
    [SugarColumn(ColumnName = "examination_method", ColumnDescription = "检查方法")]
    public string? ExaminationMethod { get; set; }

    /// <summary>
    /// 临床意义
    /// </summary>
    [SugarColumn(ColumnName = "clinical_significance", ColumnDescription = "临床意义")]
    public string? ClinicalSignificance { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnName = "is_enabled", ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 排序号
    /// </summary>
    [SugarColumn(ColumnName = "sort_order", ColumnDescription = "排序号")]
    public int SortOrder { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    public DateTime CreationTime { get; set; }

    public override object GetIdentity()
    {
        return Id;
    }
}
