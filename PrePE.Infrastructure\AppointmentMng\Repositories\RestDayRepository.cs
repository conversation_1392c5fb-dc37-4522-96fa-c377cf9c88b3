﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Repositories;

namespace PrePE.Infrastructure.AppointmentMng.Repositories;

/// <summary>
/// 休息日仓储实现
/// </summary>
public class RestDayRepository(HealthDbContext dbContext)
    : BaseRepository<RestDay>(dbContext), IRestDayRepository
{
    /// <summary>
    /// 获取指定日期的休息日
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>休息日</returns>
    public async Task<RestDay?> GetByDateAsync(DateTime date)
    {
        return await DbContext.Db.Queryable<RestDay>()
            .FirstAsync(x => x.Date == date.Date);
    }

    /// <summary>
    /// 获取日期范围内的所有休息日
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>休息日列表</returns>
    public async Task<List<RestDay>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        return await DbContext.Db.Queryable<RestDay>()
            .Where(x => startDate.Date <= x.Date && x.Date <= endDate.Date)
            .ToListAsync();
    }

    /// <summary>
    /// 判断指定日期是否为休息日
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>如果是休息日返回true，否则返回false</returns>
    public async Task<bool> IsRestDayAsync(DateTime date)
    {
        return await DbContext.Db.Queryable<RestDay>()
            .AnyAsync(x => x.Date == date.Date);
    }

    /// <summary>
    /// 获取日期范围内的所有休息日日期
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>休息日日期列表</returns>
    public async Task<List<DateTime>> GetRestDayDatesInRangeAsync(DateTime startDate, DateTime endDate)
    {
        var restDays = await GetByDateRangeAsync(startDate, endDate);
        return restDays.Select(x => x.Date).ToList();
    }

    /// <summary>
    /// 获取日期范围内的所有工作日（非休息日）
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>工作日列表</returns>
    public async Task<List<DateTime>> GetWorkDaysInRangeAsync(DateTime startDate, DateTime endDate)
    {
        var restDays = await GetRestDayDatesInRangeAsync(startDate, endDate);

        var allDays = new List<DateTime>();
        var currentDate = startDate.Date;
        while (currentDate <= endDate.Date)
        {
            allDays.Add(currentDate);
            currentDate = currentDate.AddDays(1);
        }

        return allDays.Except(restDays).ToList();
    }
}
