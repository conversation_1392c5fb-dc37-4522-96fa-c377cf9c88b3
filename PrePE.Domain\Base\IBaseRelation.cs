﻿using System.Linq.Expressions;

namespace PrePE.Domain.Base
{
    public interface IBaseRelation<TMaster, TSlave> : IRelation<TMaster, TSlave>
        where TMaster : Entity, new()
        where TSlave : Entity, new()
    {
        Task AddAsync(TSlave slave);

        Task<int> AddReturnIdentityAsync(TSlave slave);

        Task<long> AddReturnBigIdentityAsync(TSlave slave);

        Task AddRangeAsync(List<TSlave> slaves);

        Task UpdateAsync(TSlave slave);

        Task DeleteAsync(object id);

        Task<TSlave> GetSingleAsync(Expression<Func<TSlave, bool>>? expression = null);

        Task<List<TSlave>> GetListAsync(Expression<Func<TSlave, bool>>? expression = null);
    }
}
