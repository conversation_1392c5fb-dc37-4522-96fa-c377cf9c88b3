using Microsoft.Extensions.DependencyInjection;
using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Enums;
using PrePE.Domain.AppointmentMng.Repositories;
using PrePE.WebApi.Dtos.AppointmentMng;

namespace PrePE.WebApi.Tests.AppointmentMng;

[Collection(nameof(DatabaseCollection))]
public class CreateNumberSources_NumberSourceController(CustomWebApplicationFactory factory)
     : IAsyncLifetime
{
    private readonly CustomWebApplicationFactory _factory = factory;
    private readonly NumberSourceFaker _faker = new();

    [Fact]
    public async Task CreateNumberSources_返回成功_当请求参数有效时()
    {
        // 准备测试数据
        var request = CreateValidRequest();

        // 创建客户端并发送请求
        var client = _factory.CreateClient();
        var response = await client.PostAsJsonAsync(Urls.NumberSource.CreateNumberSources, request);

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task CreateNumberSources_返回400_当请求参数无效时()
    {
        // 准备无效的测试数据（结束日期早于开始日期）
        var request = CreateValidRequest();
        request.EndDate = request.StartDate.AddDays(-1);

        // 创建客户端并发送请求
        var client = _factory.CreateClient();
        var response = await client.PostAsJsonAsync(Urls.NumberSource.CreateNumberSources, request);

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        var errorMessage = await response.Content.ReadAsStringAsync();
        errorMessage.Should().Contain("结束日期不能早于开始日期");
    }

    [Fact]
    public async Task CreateNumberSources_返回400_当特殊项目类型缺少编码和名称时()
    {
        // 准备特殊项目类型但缺少编码和名称的测试数据
        var request = CreateValidRequest(sourceType: NumberSourceType.Special);

        // 创建客户端并发送请求
        var client = _factory.CreateClient();
        var response = await client.PostAsJsonAsync(Urls.NumberSource.CreateNumberSources, request);

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        var errorMessage = await response.Content.ReadAsStringAsync();
        errorMessage.Should().Contain("特殊项目编码不能为空");
    }

    [Fact]
    public async Task CreateNumberSources_返回400_当号源项列表为空时()
    {
        // 准备号源项列表为空的测试数据
        var request = CreateValidRequest();
        request.Items.Clear();

        // 创建客户端并发送请求
        var client = _factory.CreateClient();
        var response = await client.PostAsJsonAsync(Urls.NumberSource.CreateNumberSources, request);

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        var errorMessage = await response.Content.ReadAsStringAsync();
        errorMessage.Should().Contain("号源项列表不能为空");
    }

    [Fact]
    public async Task CreateNumberSources_创建正确数量的号源_当日期范围和项目数量有效时()
    {
        // 准备测试数据 - 确保日期范围内没有休息日
        // 清理所有休息日
        var restDayRepository = _factory.Services.GetRequiredService<IRestDayRepository>();
        var startDate = DateTime.Today.AddDays(1);
        var endDate = startDate.AddDays(3);

        // 创建请求
        var request = CreateValidRequest(
            startDate: startDate,
            endDate: endDate
        );

        // 计算期望的号源数量 - 需要排除休息日
        // 获取日期范围内的休息日
        var restDays = await restDayRepository.GetRestDayDatesInRangeAsync(request.StartDate, request.EndDate);

        // 计算非休息日的数量
        var workDays = 0;
        var currentDate = request.StartDate.Date;
        while (currentDate <= request.EndDate.Date)
        {
            if (!restDays.Contains(currentDate))
            {
                workDays++;
            }
            currentDate = currentDate.AddDays(1);
        }

        var itemCount = request.Items.Count;
        var expectedCount = workDays * itemCount + restDays.Count;

        // 创建客户端并发送请求
        var client = _factory.CreateClient();
        var response = await client.PostAsJsonAsync(Urls.NumberSource.CreateNumberSources, request);

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // 验证数据库中的数据
        var repository = _factory.Services.GetRequiredService<INumberSourceRepository>();
        var numberSources = await repository.GetByDateRangeAsync(request.StartDate, request.EndDate);
        numberSources.Count.Should().Be(expectedCount);

        // 过滤掉休息日的号源
        var nonRestDayNumberSources = numberSources.Where(x => !x.IsRestDay).ToList();
        nonRestDayNumberSources.Count.Should().Be(expectedCount - restDays.Count);
    }

    [Fact]
    public async Task CreateNumberSources_正确创建特殊项目号源_当特殊项目类型有效时()
    {
        // 准备特殊项目类型的测试数据
        var request = CreateValidRequest(
            sourceType: NumberSourceType.Special,
            specialItemCode: "SPECIAL001",
            specialItemName: "特殊体检项目"
        );

        // 创建客户端并发送请求
        var client = _factory.CreateClient();
        var response = await client.PostAsJsonAsync(Urls.NumberSource.CreateNumberSources, request);

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // 验证数据库中的数据
        var repository = _factory.Services.GetRequiredService<INumberSourceRepository>();
        var numberSources = await repository.GetByDateRangeAsync(request.StartDate, request.EndDate);

        // 获取非休息日的号源
        var nonRestDayNumberSources = numberSources.Where(x => !x.IsRestDay).ToList();

        // 验证特殊项目的编码和名称
        foreach (var numberSource in nonRestDayNumberSources)
        {
            numberSource.SourceType.Should().Be(NumberSourceType.Special);
            numberSource.SpecialItemCode.Should().Be(request.SpecialItemCode);
            numberSource.SpecialItemName.Should().Be(request.SpecialItemName);
        }
    }


    private CreateNumberSourcesRequest CreateValidRequest(
        DateTime? startDate = null,
        DateTime? endDate = null,
        NumberSourceType? sourceType = null,
        string? specialItemCode = null,
        string? specialItemName = null)
    {
        return new CreateNumberSourcesRequest
        {
            StartDate = startDate ?? DateTime.Today.AddDays(1),
            EndDate = endDate ?? DateTime.Today.AddDays(3),
            SourceType = sourceType ?? NumberSourceType.Individual,
            SpecialItemCode = specialItemCode,
            SpecialItemName = specialItemName,
            Items = new List<NumberSourceItem>
            {
                new NumberSourceItem
                {
                    BeginMinute = 8 * 60, // 8:00
                    EndMinute = 12 * 60,  // 12:00
                    TotalCount = 20
                },
                new NumberSourceItem
                {
                    BeginMinute = 14 * 60, // 14:00
                    EndMinute = 18 * 60,   // 18:00
                    TotalCount = 15
                }
            }
        };
    }

    public async Task InitializeAsync()
    {
        // 确保测试开始前数据库处于干净状态
        await TestDataCleaner.CleanAsync<NumberSource>();
        await TestDataCleaner.CleanAsync<RestDay>();
    }

    public async Task DisposeAsync()
    {
        // 清理测试数据
        await TestDataCleaner.CleanAsync<NumberSource>();
        await TestDataCleaner.CleanAsync<RestDay>();
    }
}
