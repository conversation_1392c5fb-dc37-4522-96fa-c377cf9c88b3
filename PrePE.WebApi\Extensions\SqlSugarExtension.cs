﻿#define USE_SQLSERVER

using PrePE.Infrastructure;
using PrePE.Infrastructure.Core;
using SqlSugar;

namespace PrePE.WebApi.Extensions;

public static class SqlSugarExtension
{
    const string ConnectionStringKey = "PrePE";

    public static void AddSqlSugar(this IServiceCollection services, ConfigurationManager configuration)
    {
        services.AddScoped<UnitOfWorkFilter>();

        services.AddScoped<ISqlSugarClient>(p =>
        {
            SqlSugarClient sqlSugar = new(CreateConfig(configuration));
            return sqlSugar;
        });

        services.AddScoped(p =>
            p.GetService<ISqlSugarClient>()!.CreateContext<HealthDbContext>(false)
        );
    }

    private static ConnectionConfig CreateConfig(ConfigurationManager configuration)
    {
        return new ConnectionConfig()
        {
#if USE_SQLSERVER
            DbType = DbType.SqlServer,
#else
            DbType = DbType.PostgreSQL,
#endif
            ConnectionString = configuration.GetConnectionString(ConnectionStringKey),
            IsAutoCloseConnection = true,
        };
    }
}
