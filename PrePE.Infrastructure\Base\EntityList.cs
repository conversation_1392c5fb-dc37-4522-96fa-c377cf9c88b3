﻿using System.Linq.Expressions;

namespace PrePE.Infrastructure.Base;

public abstract class EntityList<M, S>(HealthDbContext dbContext, M master) : IHasMany<M, S>
    where M : Entity, new()
    where S : Entity, new()
{
    public HealthDbContext DbContext { get; } = dbContext;
    public M Master { get; protected set; } = master;

    public virtual async Task<List<S>> GetAllAsync(Expression<Func<S, bool>>? expression = null)
    {
        return await DbContext.Db.Queryable<S>().Where(expression).ToListAsync();
    }

    public virtual async Task<S?> GetSingleAsync(Expression<Func<S, bool>>? expression = null)
    {
        return await DbContext.Db.Queryable<S>().Where(expression).SingleAsync();
    }
}