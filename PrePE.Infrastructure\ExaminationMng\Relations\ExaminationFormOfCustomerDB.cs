using PrePE.Domain.CustomerMng.Entities;
using PrePE.Domain.ExaminationMng.Entities;
using PrePE.Domain.ExaminationMng.Relations;

namespace PrePE.Infrastructure.ExaminationMng.Relations;

/// <summary>
/// 客户的体检单关系实现
/// </summary>
public class ExaminationFormOfCustomerDB : BaseRelation<Customer, ExaminationForm>, IExaminationFormOfCustomer
{
    public ExaminationFormOfCustomerDB(HealthDbContext dbContext, Customer master) 
        : base(dbContext, master)
    {
    }

    /// <summary>
    /// 获取客户的体检单列表
    /// </summary>
    /// <returns>体检单列表</returns>
    public async Task<List<ExaminationForm>> GetAllAsync()
    {
        return await DbContext.Db.Queryable<ExaminationForm>()
            .Where(x => x.CustomerId == Master.Id)
            .OrderByDescending(x => x.CreationTime)
            .ToListAsync();
    }

    /// <summary>
    /// 根据预约ID获取体检单
    /// </summary>
    /// <param name="appointmentId">预约ID</param>
    /// <returns>体检单</returns>
    public async Task<ExaminationForm?> GetByAppointmentIdAsync(string appointmentId)
    {
        return await DbContext.Db.Queryable<ExaminationForm>()
            .FirstAsync(x => x.CustomerId == Master.Id && x.AppointmentId == appointmentId);
    }
}
