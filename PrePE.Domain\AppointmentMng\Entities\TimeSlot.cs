﻿//using Health.Domain.AppointmentMng.Enums;

//namespace Health.Domain.AppointmentMng.Entities;

///// <summary>
///// 时间段（定义）
///// </summary>
//public class TimeSlot
//{
//    public int BeginMinute { get; set; }

//    public int EndMinute { get; set; }


//    /// <summary>
//    /// 判断2个时间段是否相交
//    /// </summary>
//    /// <param name="slot">要比较的时间段</param>
//    /// <returns>如果两个时间段相交则返回true，否则返回false</returns>
//    public bool HasIntersection(TimeSlot slot)
//    {
//        return BeginMinute < slot.EndMinute && EndMinute > slot.BeginMinute;
//    }
//}

///// <summary>
///// 号源模版
///// </summary>
//public class NumberSourceTemplate
//{
//    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
//    public long Id { get; set; }

//    [SugarColumn(ColumnName = "begin_minute", ColumnDescription = "开始分钟数")]
//    public int BeginMinute { get; set; }

//    [SugarColumn(ColumnName = "end_minute", ColumnDescription = "结束分钟数")]
//    public int EndMinute { get; set; }

//    [SugarColumn(ColumnName = "source_type", ColumnDescription = "号源类型")]
//    public NumberSourceType SourceType { get; set; }




//    //public List<TimeSlot> Slots { get; set; } = [];


//    ///// <summary>
//    ///// 添加时间段
//    ///// </summary>
//    ///// <param name="slot">时间段</param>
//    ///// <exception cref="Exception">时间段重复</exception>
//    //public void AddSlot(TimeSlot slot)
//    //{
//    //    if (HasIntersection(slot))
//    //        throw new Exception("时间段重复");

//    //    Slots.Add(slot);
//    //}

//    ///// <summary>
//    ///// 获取时间段
//    ///// </summary>
//    ///// <returns></returns>
//    //public List<TimeSlot> GetSlots()
//    //{
//    //    return Slots;
//    //}

//    //private bool HasIntersection(TimeSlot slot)
//    //{
//    //    foreach (var item in Slots)
//    //    {
//    //        if (item.HasIntersection(slot))
//    //        {
//    //            return true;
//    //        }
//    //    }
//    //    return false;
//    //}
//}