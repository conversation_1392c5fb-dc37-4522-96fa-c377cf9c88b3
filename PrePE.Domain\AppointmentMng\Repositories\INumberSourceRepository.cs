﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Enums;

namespace PrePE.Domain.AppointmentMng.Repositories
{
    public interface INumberSourceRepository : IBaseRepository<NumberSource>
    {
        /// <summary>
        /// 获取指定日期的号源
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>号源列表</returns>
        Task<List<NumberSource>> GetByDateAsync(DateTime date);

        /// <summary>
        /// 获取指定日期范围的号源
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>号源列表</returns>
        Task<List<NumberSource>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 获取指定日期和时间段的号源
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="timeSlotId">时间段ID</param>
        /// <returns>号源列表</returns>
        Task<List<NumberSource>> GetByDateAndTimeSlotAsync(DateTime date, int timeSlotId);

        /// <summary>
        /// 获取指定日期、时间段和号源类型的号源
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="timeSlotId">时间段ID</param>
        /// <param name="sourceType">号源类型</param>
        /// <returns>号源</returns>
        Task<NumberSource?> GetByDateAndTimeSlotAndTypeAsync(DateTime date, int timeSlotId, NumberSourceType sourceType);

        /// <summary>
        /// 检查指定日期、时间段和号源类型的号源是否存在
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="sourceType">号源类型</param>
        /// <returns>如果存在返回true，否则返回false</returns>
        Task<bool> ExistsAsync(DateTime date, NumberSourceType sourceType);
    }
}
