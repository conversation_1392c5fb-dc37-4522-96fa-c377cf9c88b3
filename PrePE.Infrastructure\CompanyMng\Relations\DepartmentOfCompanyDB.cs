using PrePE.Domain.CompanyMng.Entities;
using PrePE.Domain.CompanyMng.Relations;

namespace PrePE.Infrastructure.CompanyMng.Relations;

/// <summary>
/// 公司的部门关系实现
/// </summary>
public class DepartmentOfCompanyDB(HealthDbContext dbContext, Company master)
    : BaseRelation<Company, Department>(dbContext, master), IDepartmentOfCompany
{
    /// <summary>
    /// 添加部门时自动设置公司ID
    /// </summary>
    /// <param name="department">部门</param>
    /// <returns></returns>
    public override async Task AddAsync(Department department)
    {
        department.CompanyId = Master.Id;
        await base.AddAsync(department);
    }
}
