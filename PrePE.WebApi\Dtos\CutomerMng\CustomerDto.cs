﻿#pragma warning disable CS8618

namespace PrePE.WebApi.Dtos.CutomerMng;

public class CustomerDto
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime Birthday { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public int Sex { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    public string PhoneNo { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    public string? CardNo { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public int? CardType { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public string? Job { get; set; }

    /// <summary>
    /// 婚姻状态
    /// </summary>
    public string? Marriage { get; set; }

    /// <summary>
    /// 民族
    /// </summary>
    public string? Nationality { get; set; }
}


/// <summary>
/// 客户分页查询入参
/// </summary>
public class GetCustomerPageInput
{
    public int PageIndex { get; set; }
    public int PageSize { get; set; }

    /// <summary>
    /// 查询关键字（姓名或手机号）
    /// </summary>
    public string? Keyword { get; set; }
}