﻿﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Relations;
using PrePE.Domain.CustomerMng.Entities;
using PrePE.Infrastructure.Base;

namespace PrePE.Infrastructure.AppointmentMng.Relations
{
    public class AppointmentOfCustomerDB : BaseRelation<Customer, Appointment>, IAppointmentOfCustomer
    {
        public AppointmentOfCustomerDB(HealthDbContext dbContext, Customer master) 
            : base(dbContext, master)
        {
        }
    }
}
