using PrePE.Domain.CompanyMng.Entities;

namespace PrePE.Domain.CompanyMng.Roles;

/// <summary>
/// 公司管理角色
/// </summary>
/// <param name="company">公司</param>
/// <param name="departments">部门关系</param>
public class CompanyManager(Company company)
{
    public Company Company { get; } = company;

    /// <summary>
    /// 创建部门
    /// </summary>
    /// <param name="name">部门名称</param>
    /// <returns>部门</returns>
    public async Task<Department> CreateDepartmentAsync(string name)
    {
        Department dept = new()
        {
            CompanyId = Company.Id,
            Name = name
        };
        dept.Id = await Company.Departments!.AddReturnBigIdentityAsync(dept);

        return dept;
    }

    /// <summary>
    /// 更新部门
    /// </summary>
    /// <param name="department">部门</param>
    /// <returns></returns>
    public async Task UpdateDepartmentAsync(Department department)
    {
        await Company.Departments!.UpdateAsync(department);
    }

    /// <summary>
    /// 删除部门
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <returns></returns>
    public async Task DeleteDepartmentAsync(long id)
    {
        await Company.Departments!.DeleteAsync(id);
    }

    /// <summary>
    /// 获取部门列表
    /// </summary>
    /// <returns>部门列表</returns>
    public async Task<List<Department>> GetDepartmentsAsync()
    {
        return await Company.Departments!.GetListAsync();
    }

    /// <summary>
    /// 根据部门ID获取部门
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <returns>部门</returns>
    public async Task<Department?> GetDepartmentAsync(long id)
    {
        return await Company.Departments!.GetSingleAsync(d => d.Id == id);
    }
}
