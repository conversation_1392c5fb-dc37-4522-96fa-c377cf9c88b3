﻿using PrePE.Domain.SystemMng.Entities;
using PrePE.WebApi.Dtos.CutomerMng;
using PrePE.WebApi.Dtos.SystemMng;

namespace PrePE.WebApi.Tests.SystemMng.RoleControllerTests
{
    [Collection(nameof(DatabaseCollection))]
    public class Update_RoleControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private readonly Faker<RoleDto> _faker = new Faker<RoleDto>()
            .RuleFor(x => x.Name, faker => faker.Commerce.Department())
            .RuleFor(x => x.MenuIds, faker => Enumerable.Range(0, 10)
                                                        .Select(_ => faker.Random.Int(1, 10))
                                                        .Distinct()
                                                        .ToArray())
        ;
        private readonly string _token = JwtFixtures.GenerateToken4Admin();


        [Fact]
        public async Task Update_返回200_当数据有效时()
        {
            var client = _factory.CreateClient(_token);
            var role = _faker.Generate();
            var response = await client.PostAsJsonAsync(Urls.Role.Create, role);
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            role = await response.Content.ReadFromJsonAsync<RoleDto>();
            role!.Id.Should().BeGreaterThan(0);
            role!.MenuIds = [];

            response = await client.PostAsJsonAsync(Urls.Role.Update, role);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task Update_返回400_当数据无效时()
        {
            var client = _factory.CreateClient(_token);
            var role = _faker.Generate();
            var response = await client.PostAsJsonAsync(Urls.Role.Create, role);
            role = await response.Content.ReadFromJsonAsync<RoleDto>();
            role!.Name = "";

            response = await client.PostAsJsonAsync(Urls.Role.Update, role);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            string msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain("Name不能为空");
        }


        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Role>();
        }
    }
}