using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Repositories;
using PrePE.Domain.CustomerMng.Repositories;
using PrePE.Domain.ExaminationMng.Entities;
using PrePE.Domain.ExaminationMng.Enums;
using PrePE.Domain.ExaminationMng.Repositories;
using PrePE.Infrastructure.CustomerMng;
using PrePE.WebApi.Dtos.AppointmentMng;
using PrePE.WebApi.Middlewares;

namespace PrePE.WebApi.Controllers.AppointmentMng;

public class AppointmentController(
    IAppointmentRepository appointmentRepository,
    ICustomerRepository customerRepository,
    IExaminationItemRepository examinationItemRepository,
    CustomerContext customerContext,
    IMapper mapper)
    : BaseController
{
    private readonly IAppointmentRepository _appointmentRepository = appointmentRepository;
    private readonly ICustomerRepository _customerRepository = customerRepository;
    private readonly IExaminationItemRepository _examinationItemRepository = examinationItemRepository;
    private readonly CustomerContext _customerContext = customerContext;
    private readonly IMapper _mapper = mapper;

    /// <summary>
    /// 获取预约信息
    /// </summary>
    /// <param name="id">预约ID</param>
    /// <returns>预约信息</returns>
    [HttpGet]
    public async Task<ActionResult<AppointmentDto>> Get([FromQuery] string id)
    {
        var appointment = await _appointmentRepository.GetSingleAsync(x => x.Id == id);
        if (appointment is null)
            return Problem404Result();

        return Ok(_mapper.Map<AppointmentDto>(appointment));
    }

    /// <summary>
    /// 获取客户的所有预约
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <returns>预约列表</returns>
    [HttpGet("customer/{customerId}")]
    public async Task<ActionResult<List<AppointmentDto>>> GetByCustomerId(long customerId)
    {
        var appointments = await _appointmentRepository.GetListAsync(x => x.CustomerId == customerId);
        return Ok(_mapper.Map<List<AppointmentDto>>(appointments));
    }

    /// <summary>
    /// 分页获取预约列表
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="date">可选的日期筛选</param>
    /// <returns>预约列表和总数</returns>
    [HttpGet("page")]
    public async Task<ActionResult<object>> GetPage([FromQuery] int pageIndex = 1, [FromQuery] int pageSize = 10, [FromQuery] DateTime? date = null)
    {
        var (list, totalNumber) = await _appointmentRepository.GetPageAsync(
            pageIndex,
            pageSize,
            date.HasValue ? x => x.AppointmentDate.Date == date.Value.Date : null);

        return Ok(new {
            list = _mapper.Map<List<AppointmentDto>>(list),
            totalNumber
        });
    }

    /// <summary>
    /// 创建预约
    /// </summary>
    /// <param name="request">预约信息</param>
    /// <returns>创建的预约</returns>
    [HttpPost("create")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<CreateAppointmentRequest>))]
    public async Task<ActionResult<AppointmentDto>> Create([FromBody] CreateAppointmentRequest request)
    {
        // 查询客户
        var customer = await _customerRepository.GetSingleAsync(x => x.Id == request.CustomerId);
        if (customer is null)
            return Problem404Result("客户不存在");

        // 创建预约实体
        var appointment = _mapper.Map<Appointment>(request);
        appointment.CreationTime = DateTime.Now;
        appointment.Status = 0; // 初始状态

        // 将客户转换为AppointmentCustomer角色
        var appointmentCustomer = _customerContext.AsAppointmentCustomer(customer);

        // 调用角色的PrebookAsync方法完成预约
        await appointmentCustomer.PrebookAsync(appointment);

        // 自动创建体检单
        try
        {
            // 获取基础体检项目（可以根据业务需求调整）
            var basicItems = await _examinationItemRepository.GetByItemTypeAsync(ExaminationItemType.Basic);

            if (basicItems.Count > 0)
            {
                // 将客户转换为体检客户角色
                var examinationCustomer = _customerContext.AsExaminationCustomer(customer);

                // 创建体检单
                var examinationForm = await examinationCustomer.CreateExaminationFormAsync(
                    appointment.Id,
                    appointment.AppointmentDate,
                    ExaminationType.Individual,
                    basicItems);

                // 设置体检单ID到预约中
                appointment.ExaminationFormId = examinationForm.Id;
                await _appointmentRepository.UpdateAsync(appointment);
            }
        }
        catch (Exception ex)
        {
            // 体检单创建失败不影响预约，只记录日志
            // 这里可以添加日志记录
            Console.WriteLine($"创建体检单失败: {ex.Message}");
        }

        return Ok(_mapper.Map<AppointmentDto>(appointment));
    }

    /// <summary>
    /// 更新预约状态
    /// </summary>
    /// <param name="id">预约ID</param>
    /// <param name="status">新状态</param>
    /// <returns>更新后的预约</returns>
    [HttpPut("{id}/status/{status}")]
    public async Task<ActionResult<AppointmentDto>> UpdateStatus(string id, int status)
    {
        var appointment = await _appointmentRepository.GetSingleAsync(x => x.Id == id);
        if (appointment is null)
            return Problem404Result();

        appointment.Status = status;
        await _appointmentRepository.UpdateAsync(appointment);

        return Ok(_mapper.Map<AppointmentDto>(appointment));
    }

    /// <summary>
    /// 取消预约
    /// </summary>
    /// <param name="id">预约ID</param>
    /// <returns>操作结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Cancel(string id)
    {
        var appointment = await _appointmentRepository.GetSingleAsync(x => x.Id == id);
        if (appointment is null)
            return Problem404Result();

        await _appointmentRepository.DeleteAsync(id);

        return Ok();
    }
}