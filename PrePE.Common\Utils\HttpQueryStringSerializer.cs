﻿using System.Globalization;
using System.Reflection;
using System.Text;

namespace PrePE.Common.Utils;

public static class HttpQueryStringSerializer
{
    public static string ToHttpQueryString(this object queryParams)
    {
        if (queryParams is null)
        {
            return string.Empty;
        }

        var properties = queryParams.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
        var queryStringBuilder = new StringBuilder();

        foreach (var property in properties)
        {
            var value = property.GetValue(queryParams);
            if (value is null)
                continue;

            var strValue = value.ToString()!.Trim();
            if (string.IsNullOrEmpty(strValue))
                continue;

            var propertyName = Char.ToLower(property.Name[0], CultureInfo.InvariantCulture) + property.Name.Substring(1);
            var encodedPropertyName = Uri.EscapeDataString(propertyName);
            var encodedValue = Uri.EscapeDataString(strValue);
            if (queryStringBuilder.Length > 0)
                queryStringBuilder.Append('&');
            queryStringBuilder.Append($"{encodedPropertyName}={encodedValue}");
        }

        return queryStringBuilder.ToString();
    }
}
