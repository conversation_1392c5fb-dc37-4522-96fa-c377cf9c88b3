﻿using AutoMapper;
using System.Reflection;

namespace PrePE.WebApi.Extensions
{
    public static class AutoMapperExtension
    {
        public static IServiceCollection AddAutoMapperProfiles(this IServiceCollection services)
        {
            var assembly = Assembly.Load(typeof(AutoMapperExtension).Assembly.GetName());

            var profileTypes = assembly.GetTypes()
                .Where(t => typeof(Profile).IsAssignableFrom(t));

            foreach (var profileType in profileTypes)
            {
                services.AddSingleton(profileType);
            }

            services.AddAutoMapper(profileTypes.ToArray());

            return services;
        }
    }
}
