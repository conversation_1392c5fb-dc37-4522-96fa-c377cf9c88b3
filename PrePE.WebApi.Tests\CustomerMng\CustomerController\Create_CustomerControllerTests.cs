﻿using PrePE.CodeFirst.Initializer;
using PrePE.Domain.CustomerMng.Entities;
using PrePE.WebApi.Dtos.CutomerMng;

namespace PrePE.WebApi.Tests.CustomerMng.CustomerController
{
    [Collection(nameof(DatabaseCollection))]
    public class Create_CustomerControllerTests(CustomWebApplicationFactory factory)
         : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private readonly CustomerDtoFaker _faker = new();


        [Fact]
        public async Task Create_返回创建实体_当信息有效且填写手机信息时()
        {
            var customer = _faker.Generate();
            customer.CardNo = null;
            customer.CardType = null;
            var client = _factory.CreateClient();

            var response = await client.PostAsJsonAsync(Urls.Customer.Create, customer);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var result = await response.Content.ReadFromJsonAsync<CustomerDto>();
            result!.Id.Should().BeGreaterThan(0);
        }


        //[Fact]
        //public async Task Create_返回创建实体_当信息有效且填写证件信息时()
        //{
        //    var customer = _faker.Generate();
        //    customer.PhoneNo = null;
        //    var client = _factory.CreateClient();

        //    var response = await client.PostAsJsonAsync(Urls.Customer.Create, customer);

        //    response.StatusCode.Should().Be(HttpStatusCode.OK);
        //    var result = await response.Content.ReadFromJsonAsync<CustomerDto>();
        //    result!.Id.Should().BeGreaterThan(0);
        //}


        //[Fact]
        //public async Task Create_返回400_当手机号和证件信息同时为空时()
        //{
        //    var invalid = _faker.Generate();
        //    invalid.PhoneNo = null;
        //    invalid.CardNo = null;
        //    invalid.CardType = null;
        //    var client = _factory.CreateClient();

        //    var response = await client.PostAsJsonAsync(Urls.Customer.Create, invalid);

        //    response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        //    string msg = await response.Content.ReadAsStringAsync();
        //    msg.Should().Contain("PhoneNo和CardNo、CardType组合不能同时为空");
        //}


        [Fact]
        public async Task Create_返回400_当生日小于1900年()
        {
            var ancient = _faker.Generate();
            ancient.Birthday = new DateTime(1899, 12, 31);
            var client = _factory.CreateClient();

            var response = await client.PostAsJsonAsync(Urls.Customer.Create, ancient);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            var msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain($"不能小于1900-01-01");
        }


        [Fact]
        public async Task Create_返回400_当生日大于现在时()
        {
            var future = _faker.Generate();
            future.Birthday = DateTime.Now.AddDays(1);
            var client = _factory.CreateClient();

            var response = await client.PostAsJsonAsync(Urls.Customer.Create, future);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            var msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain($"不能大于当前日期");
        }


        [Fact]
        public async Task Create_返回400_当姓名为空时()
        {
            var customer = _faker.Generate();
            customer.Name = "";
            var client = _factory.CreateClient();

            var response = await client.PostAsJsonAsync(Urls.Customer.Create, customer);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            var msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain($"{nameof(customer.Name)}不能为空");
        }


        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Customer>();
        }
    }
}
