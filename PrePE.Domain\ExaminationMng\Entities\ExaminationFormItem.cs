using PrePE.Domain.ExaminationMng.Enums;

namespace PrePE.Domain.ExaminationMng.Entities;

/// <summary>
/// 体检单项目
/// </summary>
[SugarTable("exam_examination_form_item", TableDescription = "体检单项目")]
[SugarIndex("index_{table}_form_id", nameof(FormId), OrderByType.Asc)]
[SugarIndex("index_{table}_item_id", nameof(ItemId), OrderByType.Asc)]
public class ExaminationFormItem : Entity, IAggregateRoot
{
    /// <summary>
    /// ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 体检单ID
    /// </summary>
    [SugarColumn(ColumnName = "form_id", ColumnDescription = "体检单ID")]
    public string FormId { get; set; } = string.Empty;

    /// <summary>
    /// 体检项目ID
    /// </summary>
    [SugarColumn(ColumnName = "item_id", ColumnDescription = "体检项目ID")]
    public long ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    [SugarColumn(ColumnName = "item_code", ColumnDescription = "项目编码")]
    public string ItemCode { get; set; } = string.Empty;

    /// <summary>
    /// 项目名称
    /// </summary>
    [SugarColumn(ColumnName = "item_name", ColumnDescription = "项目名称")]
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// 项目类型
    /// </summary>
    [SugarColumn(ColumnName = "item_type", ColumnDescription = "项目类型")]
    public ExaminationItemType ItemType { get; set; }

    /// <summary>
    /// 项目价格
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "项目价格")]
    public decimal Price { get; set; }

    /// <summary>
    /// 数量（主要用于耗材）
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量")]
    public int Quantity { get; set; } = 1;

    /// <summary>
    /// 小计金额
    /// </summary>
    [SugarColumn(ColumnName = "subtotal", ColumnDescription = "小计金额")]
    public decimal Subtotal { get; set; }

    /// <summary>
    /// 检查状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "检查状态")]
    public ExaminationItemStatus Status { get; set; }

    /// <summary>
    /// 检查结果
    /// </summary>
    [SugarColumn(ColumnName = "result", ColumnDescription = "检查结果")]
    public string? Result { get; set; }

    /// <summary>
    /// 检查医生ID
    /// </summary>
    [SugarColumn(ColumnName = "doctor_id", ColumnDescription = "检查医生ID")]
    public long? DoctorId { get; set; }

    /// <summary>
    /// 检查医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "doctor_name", ColumnDescription = "检查医生姓名")]
    public string? DoctorName { get; set; }

    /// <summary>
    /// 检查时间
    /// </summary>
    [SugarColumn(ColumnName = "examination_time", ColumnDescription = "检查时间")]
    public DateTime? ExaminationTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remarks", ColumnDescription = "备注")]
    public string? Remarks { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    public DateTime CreationTime { get; set; }

    public override object GetIdentity()
    {
        return Id;
    }
}
