﻿using System.Text;

namespace PrePE.Common.Framework.Encryption;

public class Base64Encryptor : IEncryptor
{
    public string Decrypt(string cipherText)
    {
        byte[] plainBytes = Convert.FromBase64String(cipherText);
        string plainText = Encoding.UTF8.GetString(plainBytes);
        return plainText;
    }

    public string Encrypt(string plainText)
    {
        byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
        string cipherText = Convert.ToBase64String(plainBytes);
        return cipherText;
    }
}
