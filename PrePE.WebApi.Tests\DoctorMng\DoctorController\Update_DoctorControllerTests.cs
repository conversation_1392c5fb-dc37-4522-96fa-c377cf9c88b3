﻿using PrePE.Domain.DoctorMng.Entities;
using PrePE.WebApi.Dtos.DoctorMng;

namespace PrePE.WebApi.Tests.DoctorMng.DoctorController
{
    [Collection(nameof(DatabaseCollection))]
    public class Update_DoctorControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private static readonly Faker<CreateDoctorRequest> _faker = new Faker<CreateDoctorRequest>()
             .RuleFor(x => x.Name, faker => faker.Name.FullName())
             .RuleFor(x => x.UserName, faker => faker.Internet.UserName())
             .RuleFor(x => x.Password, faker => faker.Internet.Password())
             .RuleFor(x => x.AutographUrl, faker => faker.Internet.Url())
             .RuleFor(x => x.RoleId, faker => faker.Random.Number(1, 10))
         ;

        private DoctorDto? _stubDoctor;


        [Fact]
        public async Task Update_返回200_当数据有效时()
        {
            var client = _factory.CreateClient();

            var response = await client.PostAsJsonAsync(Urls.Doctor.Update, _stubDoctor);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task Update_返回400_当姓名为空时()
        {
            var client = _factory.CreateClient();
            var oldName = _stubDoctor!.Name;
            _stubDoctor.Name = "";

            var response = await client.PostAsJsonAsync(Urls.Doctor.Update, _stubDoctor);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            var msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain("Name不能为空");

            _stubDoctor.Name = oldName;
        }

        [Fact]
        public async Task Update_返回400_当用户名无效时()
        {
            var client = _factory.CreateClient();
            var oldUserName = _stubDoctor!.UserName;
            _stubDoctor.UserName = "";

            var response = await client.PostAsJsonAsync(Urls.Doctor.Update, _stubDoctor);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            var msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain("UserName至少包含5个字符");

            _stubDoctor.UserName = oldUserName;
        }

        [Fact]
        public async Task Update_返回404_当信息不存在时()
        {
            var client = _factory.CreateClient();
            _stubDoctor!.Id += 1;

            var response = await client.PostAsJsonAsync(Urls.Doctor.Update, _stubDoctor);

            response.StatusCode.Should().Be(HttpStatusCode.NotFound);

            _stubDoctor!.Id -= 1;
        }


        public async Task InitializeAsync()
        {
            var client = _factory.CreateClient(JwtFixtures.GenerateToken4Admin());
            var req = _faker.Generate();

            var response = await client.PostAsJsonAsync(Urls.Doctor.Create, req);

            _stubDoctor = await response.Content.ReadFromJsonAsync<DoctorDto>();
        }

        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Doctor>();
        }
    }
}
