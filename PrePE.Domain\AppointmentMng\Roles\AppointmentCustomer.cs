using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Relations;
using PrePE.Domain.CustomerMng.Entities;

namespace PrePE.Domain.AppointmentMng.Roles;

/// <summary>
/// 预约客户
/// </summary>
/// <param name="customer"></param>
/// <param name="appointments"></param>
public class AppointmentCustomer(Customer customer, IAppointmentOfCustomer appointments)
{
    public Customer Customer { get; } = customer;

    public IAppointmentOfCustomer Appointments { get; } = appointments;

    /// <summary>
    /// 预约
    /// </summary>
    /// <param name="appointment"></param>
    /// <returns></returns>
    public async Task PrebookAsync(Appointment appointment)
    {
        appointment.Id = Appointment.GenarateId();
        await Appointments.AddAsync(appointment);
    }
}