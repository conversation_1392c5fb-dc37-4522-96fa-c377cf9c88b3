﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Enums;
using PrePE.Domain.AppointmentMng.Repositories;

namespace PrePE.Infrastructure.AppointmentMng.Repositories
{
    public class NumberSourceRepository(HealthDbContext dbContext)
        : BaseRepository<NumberSource>(dbContext), INumberSourceRepository
    {
        public async Task<List<NumberSource>> GetByDateAsync(DateTime date)
        {
            var dateOnly = date.Date;
            return await DbContext.Db.Queryable<NumberSource>()
                .Where(x => x.ScheduleDate == dateOnly)
                .ToListAsync();
        }

        public async Task<List<NumberSource>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await DbContext.Db.Queryable<NumberSource>()
                .Where(x => startDate.Date <= x.ScheduleDate && x.ScheduleDate <= endDate.Date)
                .ToListAsync();
        }

        public async Task<List<NumberSource>> GetByDateAndTimeSlotAsync(DateTime date, int timeSlotId)
        {
            var dateOnly = date.Date;
            return await DbContext.Db.Queryable<NumberSource>()
                .Where(x => x.ScheduleDate == dateOnly)
                .ToListAsync();
        }

        public async Task<NumberSource?> GetByDateAndTimeSlotAndTypeAsync(DateTime date, int timeSlotId, NumberSourceType sourceType)
        {
            var dateOnly = date.Date;
            return await DbContext.Db.Queryable<NumberSource>()
                .Where(x => x.ScheduleDate == dateOnly && x.SourceType == sourceType)
                .FirstAsync();
        }

        public async Task<bool> ExistsAsync(DateTime date, int timeSlotId, NumberSourceType sourceType)
        {
            var dateOnly = date.Date;
            return await DbContext.Db.Queryable<NumberSource>()
                .AnyAsync(x => x.ScheduleDate == dateOnly && x.SourceType == sourceType);
        }

        public Task<bool> ExistsAsync(DateTime date, NumberSourceType sourceType)
        {
            throw new NotImplementedException();
        }
    }
}
