using PrePE.Domain.FileMng.Ropositories;
using Microsoft.AspNetCore.Mvc;

namespace PrePE.WebApi.Controllers.FileMng;

public class FileResourceController (IMapper mapper,  IFileResourceRepository fileResourceRepository ,  ILogger<FileResourceController> logger) : BaseController
{
    private readonly IFileResourceRepository _fileResourceRepository = fileResourceRepository;
    private readonly ILogger<FileResourceController> _logger = logger;
    private readonly   IMapper _mapper = mapper;


    [HttpPost("UploadFile")]
    public async Task<IActionResult> UploadFile(IFormFile file)
    {
        var fileResource = await _fileResourceRepository.UploadFullFilesAsync(file);
        return Ok(fileResource);
    }
}