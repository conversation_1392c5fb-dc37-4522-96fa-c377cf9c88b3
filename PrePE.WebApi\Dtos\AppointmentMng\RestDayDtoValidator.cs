﻿﻿﻿using FluentValidation;
using PrePE.WebApi.Dtos.AppointmentMng;

namespace PrePE.WebApi.Dtos.AppointmentMng
{
    /// <summary>
    /// 创建休息日请求验证器
    /// </summary>
    public class CreateRestDayRequestValidator : AbstractValidator<CreateRestDayRequest>
    {
        public CreateRestDayRequestValidator()
        {
            RuleFor(x => x.Date)
                .NotEmpty().WithMessage("日期不能为空");

            RuleFor(x => x.Type)
                .IsInEnum().WithMessage("休息日类型无效");
        }
    }
    
    /// <summary>
    /// 休息日DTO验证器
    /// </summary>
    public class RestDayDtoValidator : AbstractValidator<RestDayDto>
    {
        public RestDayDtoValidator()
        {
            RuleFor(x => x.Date)
                .NotEmpty().WithMessage("日期不能为空");

            RuleFor(x => x.Type)
                .IsInEnum().WithMessage("休息日类型无效");
        }
    }
}
