﻿using PrePE.Domain.ExaminationMng.Enums;

namespace PrePE.Domain.ExaminationMng.Entities;

/// <summary>
/// 体检单
/// </summary>
[SugarTable("exam_examination_form", TableDescription = "体检单")]
[SugarIndex("index_{table}_customer_id", nameof(CustomerId), OrderByType.Asc)]
[SugarIndex("index_{table}_appointment_id", nameof(AppointmentId), OrderByType.Asc)]
public class ExaminationForm : Entity, IAggregateRoot
{
    /// <summary>
    /// 体检单ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 客户ID
    /// </summary>
    [SugarColumn(ColumnName = "customer_id", ColumnDescription = "客户ID")]
    public long CustomerId { get; set; }

    /// <summary>
    /// 客户姓名
    /// </summary>
    [SugarColumn(ColumnName = "customer_name", ColumnDescription = "客户姓名")]
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 预约ID
    /// </summary>
    [SugarColumn(ColumnName = "appointment_id", ColumnDescription = "预约ID")]
    public string AppointmentId { get; set; } = string.Empty;

    /// <summary>
    /// 体检日期
    /// </summary>
    [SugarColumn(ColumnName = "examination_date", ColumnDescription = "体检日期")]
    public DateTime ExaminationDate { get; set; }

    /// <summary>
    /// 体检状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "体检状态")]
    public ExaminationStatus Status { get; set; }

    /// <summary>
    /// 体检类型
    /// </summary>
    [SugarColumn(ColumnName = "examination_type", ColumnDescription = "体检类型")]
    public ExaminationType ExaminationType { get; set; }

    /// <summary>
    /// 总费用
    /// </summary>
    [SugarColumn(ColumnName = "total_amount", ColumnDescription = "总费用")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 已支付金额
    /// </summary>
    [SugarColumn(ColumnName = "paid_amount", ColumnDescription = "已支付金额")]
    public decimal PaidAmount { get; set; }

    /// <summary>
    /// 支付状态
    /// </summary>
    [SugarColumn(ColumnName = "payment_status", ColumnDescription = "支付状态")]
    public PaymentStatus PaymentStatus { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    public DateTime CreationTime { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    [SugarColumn(ColumnName = "completion_time", ColumnDescription = "完成时间")]
    public DateTime? CompletionTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remarks", ColumnDescription = "备注")]
    public string? Remarks { get; set; }

    /// <summary>
    /// 生成体检单ID
    /// </summary>
    /// <returns>体检单ID</returns>
    public static string GenerateId()
    {
        // 格式：TJ + 年月日 + 6位随机数
        var dateStr = DateTime.Now.ToString("yyyyMMdd");
        var random = new Random().Next(100000, 999999);
        return $"TJ{dateStr}{random}";
    }

    public override object GetIdentity()
    {
        return Id;
    }
}
