﻿using PrePE.CodeFirst.Initializer;
using PrePE.Domain.CustomerMng.Entities;
using PrePE.WebApi.Dtos.CutomerMng;

namespace PrePE.WebApi.Tests.CustomerMng.CustomerController
{
    [Collection(nameof(DatabaseCollection))]
    public class ImportByExcel_CustomerControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private readonly CustomerDtoFaker _faker = new();


        //[Fact]
        //public async Task ImportByExcel_返回200_当数据都有效时()
        //{
        //    List<CustomerDto> customers = [];
        //    customers.AddRange(_faker.GenerateBetween(5, 10));
        //    var client = _factory.CreateClient();

        //    var response = await client.PostAsJsonAsync(Urls.Customer.ImportByExcel,
        //        new ImportByExcelDto() { Items = customers });

        //    response.StatusCode.Should().Be(HttpStatusCode.OK);
        //}


        //[Fact]
        //public async Task ImportByExcel_返回400_当有无效数据时()
        //{
        //    List<CustomerDto> customers = [];
        //    customers.AddRange(_faker.GenerateBetween(5, 10));
        //    customers[3].Name = "";
        //    var client = _factory.CreateClient();

        //    var response = await client.PostAsJsonAsync(Urls.Customer.ImportByExcel,
        //        new ImportByExcelDto() { Items = customers });

        //    response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        //}


        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }


        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Customer>();
        }
    }
}
