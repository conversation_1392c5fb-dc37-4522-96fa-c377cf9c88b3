﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <UserSecretsId>fb247a6a-43f3-4366-801b-6eab52a1763c</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>True</TreatWarningsAsErrors>
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TreatWarningsAsErrors>True</TreatWarningsAsErrors>
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Dtos\FileMng\**" />
    <Content Remove="Dtos\FileMng\**" />
    <EmbeddedResource Remove="Dtos\FileMng\**" />
    <None Remove="Dtos\FileMng\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Autofac" Version="8.1.1" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="FluentValidation" Version="11.10.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.10.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.6" />
    <PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.3.8" />
    <PackageReference Include="NewLife.Redis.Core" Version="1.9.2023.1211" />
    <PackageReference Include="NLog.Database" Version="5.4.0" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.4.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PrePE.Domain\PrePE.Domain.csproj" />
    <ProjectReference Include="..\PrePE.Infrastructure\PrePE.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="NLog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
