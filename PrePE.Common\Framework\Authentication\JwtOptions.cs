﻿namespace PrePE.Common.Framework.Authentication;

public class JwtOptions
{
    private string? secretKey;
    private string? issuer;
    private string? audience;
    private int expired;

    public string SecretKey
    {
        get => secretKey ?? "";
        set => secretKey = value;
    }
    public string Issuer
    {
        get => issuer ?? "";
        set => issuer = value;
    }
    public string Audience
    {
        get => audience ?? "";
        set => audience = value;
    }
    public int Expired
    {
        get => expired;
        set => expired = value;
    }
}
