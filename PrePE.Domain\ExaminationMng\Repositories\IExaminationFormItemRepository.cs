using PrePE.Domain.ExaminationMng.Entities;
using PrePE.Domain.ExaminationMng.Enums;

namespace PrePE.Domain.ExaminationMng.Repositories;

/// <summary>
/// 体检单项目仓储接口
/// </summary>
public interface IExaminationFormItemRepository : IBaseRepository<ExaminationFormItem>
{
    /// <summary>
    /// 根据体检单ID获取体检项目列表
    /// </summary>
    /// <param name="formId">体检单ID</param>
    /// <returns>体检项目列表</returns>
    Task<List<ExaminationFormItem>> GetByFormIdAsync(string formId);

    /// <summary>
    /// 根据体检单ID和状态获取体检项目列表
    /// </summary>
    /// <param name="formId">体检单ID</param>
    /// <param name="status">项目状态</param>
    /// <returns>体检项目列表</returns>
    Task<List<ExaminationFormItem>> GetByFormIdAndStatusAsync(string formId, ExaminationItemStatus status);

    ///// <summary>
    ///// 批量添加体检项目
    ///// </summary>
    ///// <param name="formItems">体检项目列表</param>
    ///// <returns></returns>
    //Task AddRangeAsync(List<ExaminationFormItem> formItems);
}
