﻿using FluentValidation;
using PrePE.Common.Utils;

namespace PrePE.WebApi.Dtos.CutomerMng
{
    public class CustomerDtoValidator : AbstractValidator<CustomerDto>
    {
        private static readonly DateTime MinBirthday = new(1900, 1, 1);

        public CustomerDtoValidator()
        {
            RuleFor(x => x.Name).NotEmpty().WithMessage("Name不能为空");
            RuleFor(x => x.Birthday)
                .Must(birthday => MinBirthday <= birthday && birthday <= DateTime.Now.Date)
                .WithMessage("Birthday不能小于1900-01-01，且不能大于当前日期");
            RuleFor(x => x.Sex)
                .Must(sex => sex == 1 || sex == 2)
                .WithMessage("Sex值无效");
            RuleFor(x => x.PhoneNo)
               .NotEmpty().WithMessage("手机号不能为空");
               //.Matches(@"^1[3-9]\d{9}$").WithMessage("请输入有效的手机号");
        }
    }
}
