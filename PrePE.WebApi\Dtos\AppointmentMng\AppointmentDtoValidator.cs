﻿﻿using FluentValidation;

namespace PrePE.WebApi.Dtos.AppointmentMng
{
    public class AppointmentDtoValidator : AbstractValidator<AppointmentDto>
    {
        public AppointmentDtoValidator()
        {
            RuleFor(x => x.CustomerId).GreaterThan(0).WithMessage("客户ID必须大于0");
            RuleFor(x => x.AppointmentDate).NotEmpty().WithMessage("预约日期不能为空");
            RuleFor(x => x.TimeSlotId).GreaterThan(0).WithMessage("时间段ID必须大于0");
        }
    }

    public class CreateAppointmentRequestValidator : AbstractValidator<CreateAppointmentRequest>
    {
        public CreateAppointmentRequestValidator()
        {
            RuleFor(x => x.CustomerId).GreaterThan(0).WithMessage("客户ID必须大于0");
            RuleFor(x => x.AppointmentDate).NotEmpty().WithMessage("预约日期不能为空");
            RuleFor(x => x.TimeSlotId).GreaterThan(0).WithMessage("时间段ID必须大于0");
        }
    }
}
