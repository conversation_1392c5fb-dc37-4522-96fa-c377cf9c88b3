using Microsoft.Extensions.DependencyInjection;
using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Enums;
using PrePE.Domain.AppointmentMng.Repositories;
using PrePE.WebApi.Dtos.AppointmentMng;
using PrePE.WebApi.Tests.Fixtures;
using PrePE.WebApi.Tests.Utils;
using System.Net;
using System.Net.Http.Json;

namespace PrePE.WebApi.Tests.AppointmentMng;

[Collection(nameof(DatabaseCollection))]
public class RestDay_NumberSourceController(CustomWebApplicationFactory factory)
     : IAsyncLifetime
{
    private readonly CustomWebApplicationFactory _factory = factory;

    [Fact]
    public async Task CreateRestDay_返回成功_当请求参数有效时()
    {
        // 准备测试数据
        var request = CreateValidRequest();

        // 创建客户端并发送请求
        var client = _factory.CreateClient();
        var response = await client.PostAsJsonAsync(Urls.NumberSource.CreateRestDay, request);

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<RestDayDto>();
        result.Should().NotBeNull();
        result!.Date.Date.Should().Be(request.Date.Date);
        result.Type.Should().Be(request.Type);
        result.Description.Should().Be(request.Description);
    }

    [Fact]
    public async Task CreateRestDay_返回400_当日期已存在时()
    {
        // 准备测试数据
        var request = CreateValidRequest();

        // 创建客户端并发送请求
        var client = _factory.CreateClient();

        // 第一次创建应该成功
        var response1 = await client.PostAsJsonAsync(Urls.NumberSource.CreateRestDay, request);
        response1.StatusCode.Should().Be(HttpStatusCode.OK);

        // 第二次创建应该失败，因为日期已存在
        var response2 = await client.PostAsJsonAsync(Urls.NumberSource.CreateRestDay, request);
        response2.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        var errorMessage = await response2.Content.ReadAsStringAsync();
        errorMessage.Should().Contain("已存在");
    }

    [Fact]
    public async Task GetRestDays_返回正确结果_当日期范围有效时()
    {
        // 准备测试数据
        var startDate = DateTime.Today;
        var endDate = startDate.AddDays(7);

        // 创建几个休息日
        var restDay1 = CreateValidRequest(startDate.AddDays(1), RestDayType.Weekend, "周末");
        var restDay2 = CreateValidRequest(startDate.AddDays(2), RestDayType.Holiday, "节假日");
        var restDay3 = CreateValidRequest(startDate.AddDays(3), RestDayType.Temporary, "临时休息");

        var client = _factory.CreateClient();
        await client.PostAsJsonAsync(Urls.NumberSource.CreateRestDay, restDay1);
        await client.PostAsJsonAsync(Urls.NumberSource.CreateRestDay, restDay2);
        await client.PostAsJsonAsync(Urls.NumberSource.CreateRestDay, restDay3);

        // 获取休息日列表
        var response = await client.GetAsync($"{Urls.NumberSource.GetRestDays}?startDate={startDate:yyyy-MM-dd}&endDate={endDate:yyyy-MM-dd}");

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<List<RestDayDto>>();
        result.Should().NotBeNull();
        result!.Count.Should().Be(3);

        // 验证休息日数据
        result.Should().Contain(x => x.Date.Date == restDay1.Date.Date && x.Type == restDay1.Type);
        result.Should().Contain(x => x.Date.Date == restDay2.Date.Date && x.Type == restDay2.Type);
        result.Should().Contain(x => x.Date.Date == restDay3.Date.Date && x.Type == restDay3.Type);
    }

    [Fact]
    public async Task DeleteRestDay_返回成功_当ID存在时()
    {
        // 准备测试数据
        var request = CreateValidRequest();

        // 创建休息日
        var client = _factory.CreateClient();
        var createResponse = await client.PostAsJsonAsync(Urls.NumberSource.CreateRestDay, request);
        createResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        var createdRestDay = await createResponse.Content.ReadFromJsonAsync<RestDayDto>();

        // 删除休息日
        var deleteResponse = await client.DeleteAsync($"{Urls.NumberSource.DeleteRestDay}?id={createdRestDay!.Id}");

        // 验证结果
        deleteResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        var content = await deleteResponse.Content.ReadAsStringAsync();
        content.Should().Contain("删除成功");

        // 验证休息日已被删除
        var repository = _factory.Services.GetRequiredService<IRestDayRepository>();
        var restDay = await repository.GetSingleAsync(x => x.Id == createdRestDay.Id);
        restDay.Should().BeNull();
    }

    [Fact]
    public async Task DeleteRestDay_返回404_当ID不存在时()
    {
        // 使用不存在的ID
        var nonExistentId = 99999;

        // 删除休息日
        var client = _factory.CreateClient();
        var response = await client.DeleteAsync($"{Urls.NumberSource.DeleteRestDay}?id={nonExistentId}");

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        var errorMessage = await response.Content.ReadAsStringAsync();
        errorMessage.Should().Contain("不存在");
    }

    [Fact]
    public async Task GetRestDays_返回400_当日期范围无效时()
    {
        // 准备无效的日期范围（结束日期早于开始日期）
        var startDate = DateTime.Today;
        var endDate = startDate.AddDays(-1);

        // 获取休息日列表
        var client = _factory.CreateClient();
        var response = await client.GetAsync($"{Urls.NumberSource.GetRestDays}?startDate={startDate:yyyy-MM-dd}&endDate={endDate:yyyy-MM-dd}");

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        var errorMessage = await response.Content.ReadAsStringAsync();
        errorMessage.Should().Contain("开始日期不能晚于结束日期");
    }

    private CreateRestDayRequest CreateValidRequest(
        DateTime? date = null,
        RestDayType? type = null,
        string? description = null)
    {
        return new CreateRestDayRequest
        {
            Date = date ?? DateTime.Today.AddDays(1),
            Type = type ?? RestDayType.Weekend,
            Description = description ?? "测试休息日"
        };
    }

    public async Task InitializeAsync()
    {
        // 确保测试开始前数据库处于干净状态
        await TestDataCleaner.CleanAsync<RestDay>();
    }

    public async Task DisposeAsync()
    {
        // 清理测试数据
        await TestDataCleaner.CleanAsync<RestDay>();
    }
}
