using PrePE.Domain.CompanyMng.Entities;
using PrePE.Domain.CompanyMng.Repositories;
using PrePE.Infrastructure.CompanyMng;
using PrePE.WebApi.Dtos.CompanyMng;
using PrePE.WebApi.Middlewares;

namespace PrePE.WebApi.Controllers.CompanyMng;

/// <summary>
/// 公司控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class CompanyController(
    IMapper mapper,
    ICompanyRepository companyRepository,
    CompanyContext companyContext) : BaseController
{
    private readonly IMapper _mapper = mapper;
    private readonly ICompanyRepository _companyRepository = companyRepository;
    private readonly CompanyContext _companyContext = companyContext;

    /// <summary>
    /// 创建公司
    /// </summary>
    /// <param name="request">创建公司请求</param>
    /// <returns>公司信息</returns>
    [HttpPost("CreateCompany")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<CreateCompanyRequest>))]
    public async Task<ActionResult<CompanyDto>> CreateCompany([FromBody] CreateCompanyRequest request)
    {
        if (await _companyRepository.ExistsCodeAsync(request.CompanyCode))
        {
            return Problem400Result($"公司编码 {request.CompanyCode} 已存在");
        }

        var company = _mapper.Map<Company>(request);
        await _companyRepository.AddAsync(company);

        return Ok(_mapper.Map<CompanyDto>(company));
    }

    /// <summary>
    /// 更新公司
    /// </summary>
    /// <param name="request">更新公司请求</param>
    /// <returns>公司信息</returns>
    [HttpPost("UpdateCompany")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<UpdateCompanyRequest>))]
    public Task<ActionResult<CompanyDto>> UpdateCompany([FromBody] UpdateCompanyRequest request)
    {
        throw new NotImplementedException();

        //var company = await _companyRepository.GetSingleAsync(x => x.Id == request.Id);
        //if (company == null)
        //{
        //    return Problem404Result($"公司ID {request.Id} 不存在");
        //}

        //await _companyRepository.UpdateAsync(company);

        //return Ok(_mapper.Map<CompanyDto>(company));
    }

    /// <summary>
    /// 获取公司详情
    /// </summary>
    /// <param name="id">公司ID</param>
    /// <returns>公司详情</returns>
    [HttpGet("GetCompany")]
    public async Task<ActionResult<CompanyDto>> GetCompany([FromQuery] long id)
    {
        var company = await _companyRepository.GetSingleAsync(x => x.Id == id);
        if (company == null)
        {
            return Problem404Result($"公司ID {id} 不存在");
        }

        return Ok(_mapper.Map<CompanyDto>(company));
    }

    /// <summary>
    /// 获取公司列表
    /// </summary>
    /// <returns>公司列表</returns>
    [HttpGet("GetCompanies")]
    public async Task<ActionResult<List<CompanyDto>>> GetCompanies()
    {
        List<Company> companies = await _companyRepository.GetListAsync();

        return Ok(_mapper.Map<List<CompanyDto>>(companies));
    }
}
