using PrePE.Domain.ExaminationMng.Entities;
using PrePE.Domain.ExaminationMng.Repositories;

namespace PrePE.Infrastructure.ExaminationMng.Repositories;

/// <summary>
/// 体检单仓储实现
/// </summary>
public class MedicalExaminationFormRepository(HealthDbContext dbContext)
    : BaseRepository<ExaminationForm>(dbContext), IMedicalExaminationFormRepository
{
    /// <summary>
    /// 根据预约ID获取体检单
    /// </summary>
    /// <param name="appointmentId">预约ID</param>
    /// <returns>体检单</returns>
    public async Task<ExaminationForm?> GetByAppointmentIdAsync(string appointmentId)
    {
        return await DbContext.Db.Queryable<ExaminationForm>()
            .FirstAsync(x => x.AppointmentId == appointmentId);
    }

    /// <summary>
    /// 根据客户ID获取体检单列表
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <returns>体检单列表</returns>
    public async Task<List<ExaminationForm>> GetByCustomerIdAsync(long customerId)
    {
        return await DbContext.Db.Queryable<ExaminationForm>()
            .Where(x => x.CustomerId == customerId)
            .OrderByDescending(x => x.CreationTime)
            .ToListAsync();
    }

    /// <summary>
    /// 根据日期范围获取体检单列表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>体检单列表</returns>
    public async Task<List<ExaminationForm>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        return await DbContext.Db.Queryable<ExaminationForm>()
            .Where(x => x.ExaminationDate >= startDate.Date && x.ExaminationDate <= endDate.Date)
            .OrderBy(x => x.ExaminationDate)
            .ToListAsync();
    }
}
