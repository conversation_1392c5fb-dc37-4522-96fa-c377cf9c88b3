using FluentValidation;

namespace PrePE.WebApi.Dtos.ExaminationMng;

/// <summary>
/// 创建体检单请求验证器
/// </summary>
public class CreateMedicalExaminationFormRequestValidator : AbstractValidator<CreateMedicalExaminationFormRequest>
{
    public CreateMedicalExaminationFormRequestValidator()
    {
        RuleFor(x => x.CustomerId)
            .GreaterThan(0).WithMessage("客户ID必须大于0");

        RuleFor(x => x.AppointmentId)
            .NotEmpty().WithMessage("预约ID不能为空");

        RuleFor(x => x.ExaminationDate)
            .NotEmpty().WithMessage("体检日期不能为空")
            .GreaterThanOrEqualTo(DateTime.Today).WithMessage("体检日期不能早于今天");

        RuleFor(x => x.ExaminationType)
            .IsInEnum().WithMessage("体检类型无效");

        RuleFor(x => x.ExaminationItemIds)
            .NotEmpty().WithMessage("体检项目不能为空")
            .Must(x => x.All(id => id > 0)).WithMessage("体检项目ID必须大于0");
    }
}
