﻿using PrePE.CodeFirst.Initializer;
using PrePE.Domain.CustomerMng.Entities;
using PrePE.WebApi.Dtos.CutomerMng;

namespace PrePE.WebApi.Tests.CustomerMng.CustomerController
{
    [Collection(nameof(DatabaseCollection))]
    public class Update_CustomerControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private readonly CustomerDtoFaker _faker = new();


        [Fact]
        public async Task Update_返回200_当信息有效且填写手机信息时()
        {
            var customer = _faker.Generate();
            var client = _factory.CreateClient();
            var response = await client.PostAsJsonAsync(Urls.Customer.Create, customer);
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            customer = await response.Content.ReadFromJsonAsync<CustomerDto>();
            customer!.Id.Should().BeGreaterThan(0);
            customer.CardNo = null;
            customer.CardType = null;

            response = await client.PostAsJsonAsync(Urls.Customer.Update, customer);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }


        [Fact]
        public async Task Update_返回200_当信息有效且填写证件信息时()
        {
            var customer = _faker.Generate();
            var client = _factory.CreateClient();
            var response = await client.PostAsJsonAsync(Urls.Customer.Create, customer);
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            customer = await response.Content.ReadFromJsonAsync<CustomerDto>();
            customer!.Id.Should().BeGreaterThan(0);
            // 保留手机号，确保手机号不为空
            // 同时确保证件信息也有值
            customer.CardNo = "123456789012345678";
            customer.CardType = 1;

            response = await client.PostAsJsonAsync(Urls.Customer.Update, customer);
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }


        [Fact]
        public async Task Update_返回400_当手机号和证件信息同时为空时()
        {
            var customer = _faker.Generate();
            customer.PhoneNo = "";  // 手机号设置为空字符串而不是null
            customer.CardNo = null;
            customer.CardType = null;
            var client = _factory.CreateClient();

            var response = await client.PostAsJsonAsync(Urls.Customer.Update, customer);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            string msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain("手机号不能为空");  // 错误信息应该是手机号不能为空
        }


        [Fact]
        public async Task Update_返回400_当生日小于1900年()
        {
            var customer = _faker.Generate();
            customer.Birthday = new DateTime(1899, 12, 31);
            var client = _factory.CreateClient();

            var response = await client.PostAsJsonAsync(Urls.Customer.Update, customer);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            var msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain($"不能小于1900-01-01");
        }


        [Fact]
        public async Task Update_返回400_当生日大于现在时()
        {
            var customer = _faker.Generate();
            customer.Birthday = DateTime.Now.AddDays(1);
            var client = _factory.CreateClient();

            var response = await client.PostAsJsonAsync(Urls.Customer.Update, customer);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            var msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain($"不能大于当前日期");
        }


        [Fact]
        public async Task Update_返回400_当姓名为空时()
        {
            var customer = _faker.Generate();
            customer.Name = "";
            var client = _factory.CreateClient();

            var response = await client.PostAsJsonAsync(Urls.Customer.Update, customer);

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            var msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain($"{nameof(customer.Name)}不能为空");
        }


        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Customer>();
        }

        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }
    }
}
