﻿﻿using PrePE.Domain.AppointmentMng.Roles;
using PrePE.Domain.CustomerMng.Entities;
using PrePE.Domain.ExaminationMng.Roles;
using PrePE.Infrastructure.AppointmentMng.Relations;
using PrePE.Infrastructure.ExaminationMng.Relations;

namespace PrePE.Infrastructure.CustomerMng
{
    public class CustomerContext
    {
        private readonly HealthDbContext _dbContext;

        public CustomerContext(HealthDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public AppointmentCustomer AsAppointmentCustomer(Customer customer)
        {
            return new AppointmentCustomer(customer, new AppointmentOfCustomerDB(_dbContext, customer));
        }

        public ExaminationCustomer AsExaminationCustomer(Customer customer)
        {
            return new ExaminationCustomer(customer, new ExaminationFormOfCustomerDB(_dbContext, customer));
        }
    }
}
