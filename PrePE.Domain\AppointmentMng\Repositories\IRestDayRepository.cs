﻿using PrePE.Domain.AppointmentMng.Entities;

namespace PrePE.Domain.AppointmentMng.Repositories;

/// <summary>
/// 休息日仓储接口
/// </summary>
public interface IRestDayRepository : IBaseRepository<RestDay>
{
    /// <summary>
    /// 获取指定日期的休息日
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>休息日</returns>
    Task<RestDay?> GetByDateAsync(DateTime date);

    /// <summary>
    /// 获取日期范围内的所有休息日
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>休息日列表</returns>
    Task<List<RestDay>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 判断指定日期是否为休息日
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>如果是休息日返回true，否则返回false</returns>
    Task<bool> IsRestDayAsync(DateTime date);

    /// <summary>
    /// 获取日期范围内的所有休息日日期
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>休息日日期列表</returns>
    Task<List<DateTime>> GetRestDayDatesInRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 获取日期范围内的所有工作日（非休息日）
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>工作日列表</returns>
    Task<List<DateTime>> GetWorkDaysInRangeAsync(DateTime startDate, DateTime endDate);
}
