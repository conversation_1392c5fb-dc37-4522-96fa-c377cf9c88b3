﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Enums;
using PrePE.Domain.AppointmentMng.Repositories;
using PrePE.WebApi.Dtos.AppointmentMng;
using PrePE.WebApi.Middlewares;

namespace PrePE.WebApi.Controllers.AppointmentMng;

public class NumberSourceController(IMapper mapper, INumberSourceRepository numberSourceRepository,
    IRestDayRepository restDayRepository)
    : BaseController
{
    private readonly IMapper _mapper = mapper;
    private readonly INumberSourceRepository _numberSourceRepository = numberSourceRepository;
    private readonly IRestDayRepository _restDayRepository = restDayRepository;


    /// <summary>
    /// 获取号源列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpGet("GetNumberSources")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<QueryNumberSourceRequest>))]
    public async Task<ActionResult<List<NumberSourceDto>>> GetNumberSources([FromQuery] QueryNumberSourceRequest request)
    {
        var numberSources = await _numberSourceRepository.GetByDateRangeAsync(request.StartDate, request.EndDate);

        return Ok(_mapper.Map<List<NumberSourceDto>>(numberSources));
    }

    /// <summary>
    /// (按日期范围批量)创建号源
    /// </summary>
    /// <param name="request">创建号源请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("CreateNumberSources")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<CreateNumberSourcesRequest>))]
    public async Task<ActionResult> CreateNumberSources([FromBody] CreateNumberSourcesRequest request)
    {
        List<NumberSource> numberSources = await AnalysisNumberSources(request);

        await _numberSourceRepository.AddRangeAsync(numberSources);

        return Ok();
    }


    [HttpPost("UpdateNumberSource")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<UpdateNumberSourceRequest>))]
    public Task<ActionResult> UpdateNumberSource([FromBody] UpdateNumberSourceRequest request)
    {
        throw new NotImplementedException();
        //NumberSource source = await AnalysisNumberSources(request);

        //await _numberSourceRepository.UpdateAsync(source);

        //return Ok();
    }


    /// <summary>
    /// 创建休息日
    /// </summary>
    /// <param name="request">创建休息日请求</param>
    /// <returns>创建的休息日</returns>
    [HttpPost("CreateRestDay")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<CreateRestDayRequest>))]
    public async Task<ActionResult<RestDayDto>> CreateRestDay([FromBody] CreateRestDayRequest request)
    {
        try
        {
            // 检查是否已存在相同日期的休息日
            var existingRestDay = await _restDayRepository.GetByDateAsync(request.Date);
            if (existingRestDay != null)
            {
                return Problem400Result($"日期 {request.Date:yyyy-MM-dd} 的休息日已存在");
            }

            // 创建休息日
            var restDay = _mapper.Map<RestDay>(request);
            restDay.CreationTime = DateTime.Now;

            // 保存休息日
            restDay.Id = await _restDayRepository.AddReturnBigIdentityAsync(restDay);

            return Ok(_mapper.Map<RestDayDto>(restDay));
        }
        catch (Exception ex)
        {
            return BadRequest(new { Message = $"创建休息日失败: {ex.Message}" });
        }
    }

    /// <summary>
    /// 删除休息日
    /// </summary>
    /// <param name="id">休息日ID</param>
    /// <returns>操作结果</returns>
    [HttpDelete("DeleteRestDay")]
    public async Task<ActionResult> DeleteRestDay([FromQuery] long id)
    {
        try
        {
            // 检查休息日是否存在
            var restDay = await _restDayRepository.GetSingleAsync(x => x.Id == id);
            if (restDay == null)
            {
                return NotFound(new { Message = $"ID为 {id} 的休息日不存在" });
            }

            // 删除休息日
            await _restDayRepository.DeleteAsync(id);

            return Ok(new { Message = $"休息日删除成功" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Message = $"删除休息日失败: {ex.Message}" });
        }
    }

    /// <summary>
    /// 获取休息日列表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>休息日列表</returns>
    [HttpGet("GetRestDays")]
    public async Task<ActionResult<List<RestDayDto>>> GetRestDays([FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
    {
        if (startDate > endDate)
            return BadRequest(new { Message = "开始日期不能晚于结束日期" });

        List<RestDay> restDays = await _restDayRepository.GetByDateRangeAsync(startDate, endDate);

        return Ok(_mapper.Map<List<RestDayDto>>(restDays));
    }

    #region 私有方法

    /// <summary>
    /// 分析创建号源请求，生成号源列表
    /// </summary>
    /// <param name="request">创建号源请求</param>
    /// <returns>号源列表</returns>
    private async Task<List<NumberSource>> AnalysisNumberSources(CreateNumberSourcesRequest request)
    {
        List<NumberSource> numberSources = [];

        List<DateTime> restDates = await _restDayRepository.GetRestDayDatesInRangeAsync(request.StartDate, request.EndDate);

        NumberSource numberSource;
        DateTime currentDate = request.StartDate.Date;
        while (currentDate <= request.EndDate)
        {
            if (restDates.Contains(currentDate))
            {
                // 休息日空号源
                numberSource = new NumberSource
                {
                    ScheduleDate = currentDate,
                    SourceType = request.SourceType,
                    IsRestDay = true,
                    CreationTime = DateTime.Now
                };
                numberSources.Add(numberSource);

                currentDate = currentDate.AddDays(1);
                continue;
            }

            foreach (var item in request.Items)
            {
                // 创建号源
                numberSource = _mapper.Map<NumberSource>(item);
                numberSource.ScheduleDate = currentDate;
                numberSource.SourceType = request.SourceType;
                numberSource.CreationTime = DateTime.Now;

                // 如果是特殊项目，设置特殊项目编码和名称
                if (request.SourceType == NumberSourceType.Special)
                {
                    numberSource.SpecialItemCode = request.SpecialItemCode;
                    numberSource.SpecialItemName = request.SpecialItemName;
                }

                numberSources.Add(numberSource);
            }

            currentDate = currentDate.AddDays(1);
        }

        return numberSources;
    }

    #endregion
}
