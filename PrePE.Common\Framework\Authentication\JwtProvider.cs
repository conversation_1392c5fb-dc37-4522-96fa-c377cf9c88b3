﻿using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace PrePE.Common.Framework.Authentication;

public class JwtProvider(JwtOptions jwtOptions) : IJwtProvider
{
    public JwtOptions Options { get; } = jwtOptions;

    public string GenerateToken(List<Claim> claims)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(Options.SecretKey));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: Options.Issuer,
            audience: Options.Audience,
            claims: claims,
            expires: DateTime.Now.AddMinutes(Options.Expired),
            signingCredentials: creds
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    //public bool ValidateToken(string token, out Dictionary<string, string> claims)
    //{
    //    claims = new Dictionary<string, string>();
    //    try
    //    {
    //        var handler = new JwtSecurityTokenHandler();
    //        var jwtToken = handler.ReadJwtToken(token);
    //        foreach (var claim in jwtToken.Claims)
    //        {
    //            claims.Add(claim.Type, claim.Value);
    //        }
    //        return true;
    //    }
    //    catch
    //    {
    //        return false;
    //    }
    //}
}
