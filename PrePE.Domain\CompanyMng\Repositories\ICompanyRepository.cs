using PrePE.Domain.CompanyMng.Entities;

namespace PrePE.Domain.CompanyMng.Repositories;

/// <summary>
/// 公司仓储接口
/// </summary>
public interface ICompanyRepository : IBaseRepository<Company>
{
    /// <summary>
    /// 根据公司编码获取公司
    /// </summary>
    /// <param name="companyCode">公司编码</param>
    /// <returns>公司</returns>
    Task<Company?> GetByCodeAsync(string companyCode);

    /// <summary>
    /// 检查公司编码是否存在
    /// </summary>
    /// <param name="companyCode">公司编码</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsCodeAsync(string companyCode);

    ///// <summary>
    ///// 检查统一社会信用代码是否存在
    ///// </summary>
    ///// <param name="creditCode">统一社会信用代码</param>
    ///// <returns>是否存在</returns>
    //Task<bool> ExistsCreditCodeAsync(string creditCode);
}
