using PrePE.Domain.ExaminationMng.Entities;

namespace PrePE.Domain.ExaminationMng.Repositories;

/// <summary>
/// 体检单仓储接口
/// </summary>
public interface IMedicalExaminationFormRepository : IBaseRepository<ExaminationForm>
{
    /// <summary>
    /// 根据预约ID获取体检单
    /// </summary>
    /// <param name="appointmentId">预约ID</param>
    /// <returns>体检单</returns>
    Task<ExaminationForm?> GetByAppointmentIdAsync(string appointmentId);

    /// <summary>
    /// 根据客户ID获取体检单列表
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <returns>体检单列表</returns>
    Task<List<ExaminationForm>> GetByCustomerIdAsync(long customerId);

    /// <summary>
    /// 根据日期范围获取体检单列表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>体检单列表</returns>
    Task<List<ExaminationForm>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);
}
