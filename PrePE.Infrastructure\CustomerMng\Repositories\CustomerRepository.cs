﻿using PrePE.Domain.CustomerMng.Entities;
using PrePE.Domain.CustomerMng.Repositories;

namespace PrePE.Infrastructure.CustomerMng.Repositories
{
    public class CustomerRepository(HealthDbContext dbContext)
        : BaseRepository<Customer>(dbContext), ICustomerRepository
    {
        //public override Task IncludeAsync(Customer entity)
        //{
        //    throw new NotImplementedException();
        //}

        public override async Task AddAsync(Customer entity)
        {
            entity.Id = await DbContext.Customers.InsertReturnBigIdentityAsync(entity);
        }

        //public override async Task<Customer> GetByIdAsync(object id)
        //{
        //    return await DbContext.Customers.GetByIdAsync(id);
        //}
    }
}
