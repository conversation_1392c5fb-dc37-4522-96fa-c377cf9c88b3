﻿namespace PrePE.WebApi.Dtos.DoctorMng
{
    /// <summary>
    /// 通用医生数据模型
    /// </summary>
    public class DoctorDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 角色Id
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// 介绍
        /// </summary>
        public string? Introduction { get; set; }
    }

    /// <summary>
    /// 创建医生信息请求模型
    /// </summary>
    public class CreateDoctorRequest
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 签名Url
        /// </summary>
        public string? AutographUrl { get; set; }

        /// <summary>
        /// 角色Id
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// 介绍
        /// </summary>
        public string? Introduction { get; set; }
    }

    /// <summary>
    /// 医生重置密码请求模型
    /// </summary>
    public class UpdatePasswordRequest
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }
    }
}
