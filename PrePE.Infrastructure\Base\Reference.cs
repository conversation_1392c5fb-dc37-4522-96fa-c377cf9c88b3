﻿
namespace PrePE.Infrastructure.Base;

public class Reference<M, S>(M m) : IHasOne<M, S>
    where M : <PERSON><PERSON><PERSON>, new()
    where S : <PERSON><PERSON><PERSON>, new()
{
    public M Master { get; protected set; } = m;

    public Task<S> GetAsync()
    {
        throw new NotImplementedException();
    }

    public Task SetAsync(S follower)
    {
        throw new NotImplementedException();
    }
}
