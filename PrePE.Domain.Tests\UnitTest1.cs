namespace PrePE.Domain.Tests
{
    public class TimeSlotTests
    {
        [Fact]
        public void HasIntersection_WhenTimeSlotsDontOverlap_ReturnsFalse()
        {
            // Arrange
            var slot1 = new TimeSlot { BeginMinute = 0, EndMinute = 30 };
            var slot2 = new TimeSlot { BeginMinute = 30, EndMinute = 60 };

            // Act
            var result = slot1.HasIntersection(slot2);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void HasIntersection_WhenTimeSlotsOverlap_ReturnsTrue()
        {
            // Arrange
            var slot1 = new TimeSlot { BeginMinute = 0, EndMinute = 40 };
            var slot2 = new TimeSlot { BeginMinute = 30, EndMinute = 60 };

            // Act
            var result = slot1.HasIntersection(slot2);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void HasIntersection_WhenOneTimeSlotContainsAnother_ReturnsTrue()
        {
            // Arrange
            var slot1 = new TimeSlot { BeginMinute = 0, EndMinute = 60 };
            var slot2 = new TimeSlot { BeginMinute = 15, EndMinute = 45 };

            // Act
            var result1 = slot1.HasIntersection(slot2);
            var result2 = slot2.HasIntersection(slot1);

            // Assert
            Assert.True(result1);
            Assert.True(result2);
        }

        [Fact]
        public void HasIntersection_WhenTimeSlotsAreAdjacent_ReturnsFalse()
        {
            // Arrange
            var slot1 = new TimeSlot { BeginMinute = 0, EndMinute = 30 };
            var slot2 = new TimeSlot { BeginMinute = 30, EndMinute = 60 };
            var slot3 = new TimeSlot { BeginMinute = 60, EndMinute = 90 };

            // Act
            var result1 = slot1.HasIntersection(slot2);
            var result2 = slot2.HasIntersection(slot3);

            // Assert
            Assert.False(result1);
            Assert.False(result2);
        }

        [Fact]
        public void HasIntersection_WhenTimeSlotsHaveSameBoundaries_ReturnsTrue()
        {
            // Arrange
            var slot1 = new TimeSlot { BeginMinute = 30, EndMinute = 60 };
            var slot2 = new TimeSlot { BeginMinute = 30, EndMinute = 60 };

            // Act
            var result = slot1.HasIntersection(slot2);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void HasIntersection_WhenOneSlotBeginsExactlyWhenAnotherEnds_ReturnsFalse()
        {
            // Arrange
            var slot1 = new TimeSlot { BeginMinute = 0, EndMinute = 30 };
            var slot2 = new TimeSlot { BeginMinute = 30, EndMinute = 60 };

            // Act
            var result1 = slot1.HasIntersection(slot2); // slot1结束时slot2开始
            var result2 = slot2.HasIntersection(slot1); // slot2开始时slot1结束

            // Assert
            Assert.False(result1);
            Assert.False(result2);
        }
    }



    public class TimeSlot
    {
        public int BeginMinute { get; set; }

        public int EndMinute { get; set; }

        /// <summary>
        /// 判断2个时间段是否相交
        /// </summary>
        /// <param name="slot">要比较的时间段</param>
        /// <returns>如果两个时间段相交则返回true，否则返回false</returns>
        public bool HasIntersection(TimeSlot slot)
        {
            return BeginMinute < slot.EndMinute && EndMinute > slot.BeginMinute;
        }
    }
}