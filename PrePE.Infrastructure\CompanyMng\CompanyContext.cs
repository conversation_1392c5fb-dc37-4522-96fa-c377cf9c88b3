using PrePE.Domain.CompanyMng.Entities;
using PrePE.Domain.CompanyMng.Roles;

namespace PrePE.Infrastructure.CompanyMng;

/// <summary>
/// 公司上下文
/// </summary>
public class CompanyContext
{
    private readonly HealthDbContext _dbContext;

    public CompanyContext(HealthDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    /// <summary>
    /// 将公司转换为公司管理角色
    /// </summary>
    /// <param name="company">公司</param>
    /// <returns>公司管理角色</returns>
    public CompanyManager AsCompanyManager(Company company)
    {
        return new CompanyManager(company);
    }
}
