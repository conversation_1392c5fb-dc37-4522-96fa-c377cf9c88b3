﻿using PrePE.Domain.AppointmentMng.Entities;
using PrePE.WebApi.Dtos.AppointmentMng;

namespace PrePE.WebApi.AutoMapperProfiles.AppointmentMng
{
    public class NumberSourceProfile : Profile
    {
        public NumberSourceProfile()
        {
            CreateMap<NumberSource, NumberSourceDto>().ReverseMap();
            CreateMap<CreateNumberSourceRequest, NumberSource>();
            CreateMap<UpdateNumberSourceRequest, NumberSource>();

            // 映射 NumberSourceItem 到 NumberSource
            CreateMap<NumberSourceItem, NumberSource>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.BookedCount, opt => opt.MapFrom(src => 0))
                .ForMember(dest => dest.IsRestDay, opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.Now));
        } 
    }
}
