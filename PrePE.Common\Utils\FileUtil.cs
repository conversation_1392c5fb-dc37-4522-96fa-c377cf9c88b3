namespace PrePE.Common.Utils;

public static class FileUtil
{
    // 创建目录，如果目录已存在则不执行任何操作
    public static void CreateDirectoryIfNotExists(string directoryPath)
    {
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath);
        }
    }

    // 删除目录及其所有内容
    public static void DeleteDirectory(string directoryPath)
    {
        if (Directory.Exists(directoryPath))
        {
            Directory.Delete(directoryPath, true);
        }
    }

    // 复制文件
    public static void CopyFile(string sourceFilePath, string destinationFilePath)
    {
        if (File.Exists(sourceFilePath))
        {
            File.Copy(sourceFilePath, destinationFilePath, true);
        }
    }

    // 移动文件
    public static void MoveFile(string sourceFilePath, string destinationFilePath)
    {
        if (File.Exists(sourceFilePath))
        {
            File.Move(sourceFilePath, destinationFilePath);
        }
    }

    // 删除文件
    public static void DeleteFile(string filePath)
    {
        if (File.Exists(filePath))
        {
            File.Delete(filePath);
        }
    }

    
    
    /// <summary>
    /// 异步创建文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">文件内容</param>
    public static async Task CreatFileAsync(string filePath, Stream content)
    {
        using (var fs =  File.OpenWrite(filePath))
        {
            await content.CopyToAsync(fs);
        }
    }

    // 异步创建空文件
    public static async Task CreateEmptyFileAsync(string filePath)
    {
        // 使用FileStream异步创建空文件，如果文件已存在则不做任何操作
        if (!File.Exists(filePath))
        {
            using (var stream = File.Create(filePath))
            {
                // 这里不需要写入任何内容，只是创建文件
                await stream.DisposeAsync();
            }
        }
    }
    
    /// <summary>
    /// 异步创建文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">文件内容</param>
    public static async Task CreatFileAsync(string filePath, byte[] content)
    {
       await File.WriteAllBytesAsync(filePath, content);
    }


    // 异步向文件追加文本
    public static async Task AppendTextAsync(string filePath,  Stream fileStream, int position = 0)
    {
        // 确保文件存在，如果不存在则创建一个空文件
        if (!File.Exists(filePath))
        {
            using (var emptyFile = File.Create(filePath))
            {
                // 创建空文件，不进行任何写入操作
                await emptyFile.DisposeAsync();
            }
        }
        
        // 以读写模式打开文件，并定位到指定位置
        using (var file = new FileStream(filePath, FileMode.Open, FileAccess.ReadWrite))
        {
            // 定位到文件的指定位置
            file.Seek(position, SeekOrigin.Begin);
            // 从指定位置开始写入流
            await fileStream.CopyToAsync(file);
        }
    }
    
    // 异步将字节数组写入到文件的指定位置
    public static async Task WriteBytesToFileAsync(string filePath, byte[] bytes, long position)
    {
        // 确保文件存在，如果不存在则创建一个空文件
        if (!File.Exists(filePath))
        {
            using (var emptyFile = File.Create(filePath))
            {
                // 创建空文件，不进行任何写入操作
                await emptyFile.DisposeAsync();
            }
        }

        // 以读写模式打开文件
        using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Write))
        {
            // 定位到文件的指定位置
            fileStream.Seek(position, SeekOrigin.Begin);

            // 异步写入字节数组
            await fileStream.WriteAsync(bytes, 0, bytes.Length);
        }
    }
}