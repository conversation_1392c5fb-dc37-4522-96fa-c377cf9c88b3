﻿﻿﻿using PrePE.Domain.AppointmentMng.Enums;

namespace PrePE.Domain.AppointmentMng.Entities;

/// <summary>
/// 休息日
/// </summary>
[SugarTable("appt_rest_day", TableDescription = "休息日")]
public class RestDay : Entity, IAggregateRoot
{
    /// <summary>
    /// ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    [SugarColumn(ColumnName = "date", ColumnDescription = "日期")]
    public DateTime Date { get; set; }

    /// <summary>
    /// 休息日类型
    /// </summary>
    [SugarColumn(ColumnName = "type", ColumnDescription = "休息日类型")]
    public RestDayType Type { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(ColumnName = "description", ColumnDescription = "描述")]
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    public DateTime? CreationTime { get; set; }

    public override object GetIdentity()
    {
        return Id;
    }
}
