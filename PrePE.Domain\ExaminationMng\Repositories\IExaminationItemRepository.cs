using PrePE.Domain.ExaminationMng.Entities;
using PrePE.Domain.ExaminationMng.Enums;

namespace PrePE.Domain.ExaminationMng.Repositories;

/// <summary>
/// 体检项目仓储接口
/// </summary>
public interface IExaminationItemRepository : IBaseRepository<ExaminationItem>
{
    /// <summary>
    /// 根据项目编码获取体检项目
    /// </summary>
    /// <param name="itemCode">项目编码</param>
    /// <returns>体检项目</returns>
    Task<ExaminationItem?> GetByItemCodeAsync(string itemCode);

    /// <summary>
    /// 根据项目类型获取体检项目列表
    /// </summary>
    /// <param name="itemType">项目类型</param>
    /// <returns>体检项目列表</returns>
    Task<List<ExaminationItem>> GetByItemTypeAsync(ExaminationItemType itemType);

    /// <summary>
    /// 获取启用的体检项目列表
    /// </summary>
    /// <returns>体检项目列表</returns>
    Task<List<ExaminationItem>> GetEnabledItemsAsync();
}
