﻿﻿#pragma warning disable CS8618

namespace PrePE.WebApi.Dtos.AppointmentMng
{
    /// <summary>
    /// 预约数据传输对象
    /// </summary>
    public class AppointmentDto
    {
        /// <summary>
        /// 预约ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public long CustomerId { get; set; }

        /// <summary>
        /// 客户姓名
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 预约日期
        /// </summary>
        public DateTime AppointmentDate { get; set; }

        /// <summary>
        /// 预约时间段ID
        /// </summary>
        public int TimeSlotId { get; set; }

        /// <summary>
        /// 预约状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 体检单Id
        /// </summary>
        public string? ExaminationFormId { get; set; }
    }

    /// <summary>
    /// 创建预约请求
    /// </summary>
    public class CreateAppointmentRequest
    {
        /// <summary>
        /// 客户Id
        /// </summary>
        public long CustomerId { get; set; }

        /// <summary>
        /// 客户姓名
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 预约日期
        /// </summary>
        public DateTime AppointmentDate { get; set; }

        /// <summary>
        /// 预约时间段ID
        /// </summary>
        public int TimeSlotId { get; set; }

        // /// <summary>
        // /// 体检单Id
        // /// </summary>
        // public string? ExaminationFormId { get; set; }
    }


    public class MedicalExamBookingDto
    {
        // 预约人信息，可根据实际情况扩展
        public string PatientName { get; set; }
        public string PatientId { get; set; }

        // 项目清单
        public List<ExamItem> ExamItems { get; set; }
    }

    public class ExamItem
    {
        // 项目唯一标识
        public string ItemId { get; set; }
        // 项目名称
        public string ItemName { get; set; }
        // 项目类型，如套餐、一般项目、耗材
        public string ItemType { get; set; }
        // 项目价格
        public decimal ItemPrice { get; set; }
        // 套餐明细
        public List<ExamItem> PackageDetails { get; set; }
        // 耗材数量，仅对耗材类型有效
        public int ConsumableQuantity { get; set; } = 1;
        // 前置项目列表
        public List<string> PrerequisiteItems { get; set; }
        // 必须同时做的项目列表
        public List<string> ConcurrentItems { get; set; }
        // 互斥项目列表
        public List<string> MutuallyExclusiveItems { get; set; }
        // 同类项目列表
        public List<string> SimilarItems { get; set; }
    }

    public class PackageDetail
    {
        // 明细项唯一标识
        public string DetailId { get; set; }
        // 明细项名称
        public string DetailName { get; set; }
        // 明细项价格
        public decimal DetailPrice { get; set; }
        // 是否选择该明细项
        public bool IsSelected { get; set; }
    }
}
