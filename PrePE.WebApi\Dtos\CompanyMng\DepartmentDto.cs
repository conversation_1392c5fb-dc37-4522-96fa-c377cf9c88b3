

namespace PrePE.WebApi.Dtos.CompanyMng;

/// <summary>
/// 部门DTO
/// </summary>
public class DepartmentDto
{
    /// <summary>
    /// 部门ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 公司ID
    /// </summary>
    public long CompanyId { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string DepartmentName { get; set; } = string.Empty;
}

/// <summary>
/// 创建部门请求
/// </summary>
public class CreateDepartmentRequest
{
    /// <summary>
    /// 公司ID
    /// </summary>
    public long CompanyId { get; set; }

    ///// <summary>
    ///// 部门编码
    ///// </summary>
    //public string DepartmentCode { get; set; } = string.Empty;

    /// <summary>
    /// 部门名称
    /// </summary>
    public string DepartmentName { get; set; } = string.Empty;
}

/// <summary>
/// 更新部门请求
/// </summary>
public class UpdateDepartmentRequest
{
    /// <summary>
    /// 部门ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string? DepartmentName { get; set; }
}
