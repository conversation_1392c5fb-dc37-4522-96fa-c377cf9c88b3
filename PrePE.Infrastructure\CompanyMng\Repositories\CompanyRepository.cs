using PrePE.Domain.CompanyMng.Entities;
using PrePE.Domain.CompanyMng.Repositories;

namespace PrePE.Infrastructure.CompanyMng.Repositories;

/// <summary>
/// 公司仓储实现
/// </summary>
public class CompanyRepository(HealthDbContext dbContext)
    : BaseRepository<Company>(dbContext), ICompanyRepository
{
    /// <summary>
    /// 根据公司编码获取公司
    /// </summary>
    /// <param name="companyCode">公司编码</param>
    /// <returns>公司</returns>
    public async Task<Company?> GetByCodeAsync(string companyCode)
    {
        return await DbContext.Db.Queryable<Company>()
            .FirstAsync(x => x.CompanyCode == companyCode);
    }

    /// <summary>
    /// 检查公司编码是否存在
    /// </summary>
    /// <param name="companyCode">公司编码</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsCodeAsync(string companyCode)
    {
        return await DbContext.Db.Queryable<Company>()
            .Where(x => x.CompanyCode == companyCode)
            .AnyAsync();
    }
}
