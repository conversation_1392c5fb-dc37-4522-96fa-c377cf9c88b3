﻿using Microsoft.Extensions.DependencyInjection;
using PrePE.Domain.AppointmentMng.Entities;
using PrePE.Domain.AppointmentMng.Repositories;
using PrePE.WebApi.Dtos.AppointmentMng;

namespace PrePE.WebApi.Tests.AppointmentMng;

[Collection(nameof(DatabaseCollection))]
public class GetNumberSources_NumberSourceController(CustomWebApplicationFactory factory)
     : IAsyncLifetime
{
    private readonly CustomWebApplicationFactory _factory = factory;
    private readonly NumberSourceFaker _faker = new();

    [Fact]
    public async Task GetNumberSources_返回非空列表_当数据存在时()
    {
        // 准备测试数据
        var startDate = DateTime.Today.AddDays(-3);
        var endDate = DateTime.Today.AddDays(3);

        // 创建5-10个测试数据
        var numberSourceCount = new Random().Next(5, 11);
        var numberSources = new List<NumberSource>();

        for (int i = 0; i < numberSourceCount; i++)
        {
            var source = _faker.Generate();
            // 确保日期在查询范围内
            source.ScheduleDate = startDate.AddDays(new Random().Next(0, (int)(endDate - startDate).TotalDays + 1));
            numberSources.Add(source);

            // 使用仓储保存数据
            var repository = _factory.Services.GetRequiredService<INumberSourceRepository>();
            await repository.AddAsync(source);
        }

        // 创建客户端并构造请求
        var client = _factory.CreateClient();
        var requestUrl = $"{Urls.NumberSource.GetNumberSources}?StartDate={startDate:yyyy-MM-dd}&EndDate={endDate:yyyy-MM-dd}";

        // 发送请求
        var response = await client.GetAsync(requestUrl);

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<List<NumberSourceDto>>();
        result.Should().NotBeNull();
        result!.Count.Should().BeGreaterThanOrEqualTo(numberSources.Count);
    }

    [Fact]
    public async Task GetNumberSources_返回空列表_当数据不存在时()
    {
        // 清空数据库中的号源数据
        await TestDataCleaner.CleanAsync<NumberSource>();

        // 使用未来的日期范围
        var startDate = DateTime.Today.AddDays(10);
        var endDate = DateTime.Today.AddDays(20);

        // 创建客户端并构造请求
        var client = _factory.CreateClient();
        var requestUrl = $"{Urls.NumberSource.GetNumberSources}?StartDate={startDate:yyyy-MM-dd}&EndDate={endDate:yyyy-MM-dd}";

        // 发送请求
        var response = await client.GetAsync(requestUrl);

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<List<NumberSourceDto>>();
        result.Should().NotBeNull();
        result!.Count.Should().Be(0);
    }

    [Fact]
    public async Task GetNumberSources_返回400_当请求参数无效时()
    {
        // 构造无效的请求参数（结束日期早于开始日期）
        var startDate = DateTime.Today.AddDays(10);
        var endDate = DateTime.Today.AddDays(5); // 结束日期早于开始日期

        // 创建客户端并构造请求
        var client = _factory.CreateClient();
        var requestUrl = $"{Urls.NumberSource.GetNumberSources}?StartDate={startDate:yyyy-MM-dd}&EndDate={endDate:yyyy-MM-dd}";

        // 发送请求
        var response = await client.GetAsync(requestUrl);

        // 验证结果
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        var errorMessage = await response.Content.ReadAsStringAsync();
        errorMessage.Should().Contain("结束日期不能早于开始日期");
    }

    public async Task InitializeAsync()
    {
        // 确保测试开始前数据库处于干净状态
        await TestDataCleaner.CleanAsync<NumberSource>();
    }

    public async Task DisposeAsync()
    {
        // 清理测试数据
        await TestDataCleaner.CleanAsync<NumberSource>();
    }
}
