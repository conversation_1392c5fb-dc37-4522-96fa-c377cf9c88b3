using AutoMapper;
using PrePE.Domain.ExaminationMng.Entities;
using PrePE.WebApi.Dtos.ExaminationMng;

namespace PrePE.WebApi.AutoMapperProfiles.ExaminationMng;

/// <summary>
/// 体检单 AutoMapper 配置
/// </summary>
public class MedicalExaminationFormProfile : Profile
{
    public MedicalExaminationFormProfile()
    {
        CreateMap<ExaminationForm, MedicalExaminationFormDto>().ReverseMap();
        CreateMap<ExaminationFormItem, ExaminationFormItemDto>().ReverseMap();
        CreateMap<ExaminationItem, ExaminationItemDto>().ReverseMap();
        
        CreateMap<CreateMedicalExaminationFormRequest, ExaminationForm>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CustomerName, opt => opt.Ignore())
            .ForMember(dest => dest.Status, opt => opt.Ignore())
            .ForMember(dest => dest.TotalAmount, opt => opt.Ignore())
            .ForMember(dest => dest.PaidAmount, opt => opt.Ignore())
            .ForMember(dest => dest.PaymentStatus, opt => opt.Ignore())
            .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.Now))
            .ForMember(dest => dest.CompletionTime, opt => opt.Ignore());
    }
}
